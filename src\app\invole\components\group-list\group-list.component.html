<div class="list">
    <div class="btn" style="display: flex; align-items: center">
        <nz-tabset [(nzSelectedIndex)]="selectedIndex" (nzSelectChange)="onTabChange($event)">
            <nz-tab nzTitle="群组列表"> </nz-tab>
            <nz-tab nzTitle="人员列表"> </nz-tab>
        </nz-tabset>
        <!-- 导入 -->
        <div class="import">
            <button nz-button nzType="primary" (click)="showPackageUploadModal()">导入</button>
        </div>
    </div>

    <nz-modal [(nzVisible)]="packageVisible" nzTitle="数据包导入" (nzOnCancel)="handlePackageUploadCancel()" [nzFooter]="null">
        <list-batch-upload></list-batch-upload>
    </nz-modal>

    <!-- 搜索 -->
    <div class="search">
        <a (click)="getBatchQuery()" *ngIf="selectedIndex === 0">群组及聊天信息批量查询</a>
        <a (click)="getBatchQueryUser()" *ngIf="selectedIndex === 1">信息批量查询</a>
        <nz-modal [nzWidth]="600" [nzFooter]="null" [(nzVisible)]="isQueryVisible" nzTitle="群组及聊天信息批量查询" (nzOnCancel)="handleCancel()">
            <div class="prompt">
                <div class="mb5">① 下载 <a id="demoExcel" href="javascript:" (click)="ModelDownload($event)" class="col-FE8E20">Excel模板文件</a> 根据模板文件的格式填写您要批量查询的群组ID</div>
                <div>② 上传文件进行查询</div>
            </div>

            <div class="time">
                <span>
                    <span style="color: #f00">*</span>
                    起止时间：</span
                >
                <nz-range-picker [(ngModel)]="dateRange" nzShowTime nzFormat="yyyy-MM-dd HH:mm:ss" [nzPlaceHolder]="['开始时间', '结束时间']" (ngModelChange)="onChange($event)" (nzOnOk)="onOk($event)"></nz-range-picker>
            </div>

            <div class="upload">
                <nz-upload nzType="drag" nzAccept=".xlsx, .xls" [nzMultiple]="true" [(nzFileList)]="fileList" [nzBeforeUpload]="beforeUpload">
                    <p class="ant-upload-drag-icon">
                        <i nz-icon nzType="inbox"></i>
                    </p>
                    <p class="ant-upload-text">点击选择文件或将文件拖拽到这里上传</p>
                    <p class="ant-upload-hint">
                        <li>仅支持Excel（xls、xlsx文件）</li>
                    </p>
                </nz-upload>
                <div class="upload-btn">
                    <button
                        nz-button
                        [nzType]="'primary'"
                        [nzLoading]="uploading"
                        (click)="handlePackageUpload()"
                        [disabled]="!start_time || !end_time || fileList.length == 0"
                        style="margin-top: 16px"
                    >
                        {{ uploading ? "上传中" : "开始上传" }}
                    </button>
                </div>
            </div>
        </nz-modal>
        <!-- 用户列表 -->
        <nz-modal [nzWidth]="600" [nzFooter]="null" [(nzVisible)]="isQueryVisibleUser" nzTitle="人员信息批量查询" (nzOnCancel)="handleCancelUser()">
            <div class="prompt">
                <div class="mb5">① 下载 <a id="demoExcel" href="javascript:" (click)="ModelDownloadUser($event)" class="col-FE8E20">Excel模板文件</a> 根据模板文件的格式填写您要批量查询的成员ID</div>
                <div>② 上传文件进行查询</div>
            </div>

            <nz-upload nzType="drag" nzAccept=".xlsx,.xls" [nzMultiple]="true" [(nzFileList)]="fileListMember" [nzBeforeUpload]="beforeUpload">
                <p class="ant-upload-drag-icon">
                    <i nz-icon nzType="inbox"></i>
                </p>
                <p class="ant-upload-text">点击选择文件或将文件拖拽到这里上传</p>
                <p class="ant-upload-hint">
                    <li>仅支持Excel（xls、xlsx文件）</li>
                </p>
            </nz-upload>
            <div class="upload-btn">
                <button nz-button [nzType]="'primary'" [nzLoading]="uploading" (click)="handleMemberPackageUpload()" [disabled]="fileListMember.length == 0" style="margin-top: 16px">
                    {{ uploading ? "上传中" : "开始上传" }}
                </button>
            </div>
        </nz-modal>
    </div>
</div>

<div class="content">
    <div class="group" *ngIf="selectedIndex === 0">
        <!-- [nzLoading]="isTableLoading" -->
        <nz-table
            #editRowTable
            nzShowSizeChanger
            [nzData]="groupsListOfData"
            [nzFrontPagination]="false"
            nzShowPagination
            [nzTotal]="groupListTotal"
            [nzShowTotal]="totalTemplate"
            [nzPageIndex]="groupListParams.pageNum"
            [nzPageSize]="groupListParams.pageSize"
            [nzBordered]="false"
            [nzPageSizeOptions]="[10, 20, 50, 100]"
            [nzShowQuickJumper]="false"
            (nzPageIndexChange)="pageSizeChanged($event)"
            (nzPageSizeChange)="pageChanged($event)"
        >
            <thead>
                <tr>
                    <th>群组名称</th>
                    <th>群组ID</th>
                    <th>近期活跃时间</th>
                    <th>
                        群组标签
                        <span class="tool-tip-wrap" style="font-size: 14px; vertical-align: 2px; cursor: pointer; margin-left: 3px">
                            <i nz-icon nzType="question-circle" nzTheme="outline" nz-tooltip nzPlacement="right" [nzTitle]="titleTemplate" [nzTrigger]="'hover'"></i>
                        </span>
                        <ng-template #titleTemplate>
                            <span>输入群组标签，用英文逗号分隔</span>
                        </ng-template>
                    </th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let data of editRowTable.data">
                    <td>
                        <span nz-tooltip [nzTitle]="'群组名称：' + data.group_name" style="max-width: 174px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block">
                            {{ data.group_name }}
                        </span>
                    </td>

                    <td>
                        {{ data.group_id }}
                    </td>
                    <td>
                        {{ data.last_active }}
                    </td>

                    <td>
                        <ng-container *ngIf="!editCache[data.id].edit; else groupInputTpl">
                            <nz-tag *ngFor="let tag of data.group_label" nz-tooltip [nzTitle]="'群组标签：' + tag">{{ tag }}</nz-tag>
                        </ng-container>
                        <ng-template #groupInputTpl>
                            <input placeholder="输入群组标签，用逗号分隔" type="text" nz-input [(ngModel)]="editCache[data.id].data.group_label" />
                        </ng-template>
                    </td>
                    <td>
                        <div class="editable-row-operations">
                            <ng-container *ngIf="!editCache[data.id].edit; else saveTpl">
                                <a (click)="showDetailGroupDetails(data)">查看群组详情</a>
                                <nz-divider nzType="vertical"></nz-divider>
                                <a (click)="startEdit(data)">编辑标签</a>
                            </ng-container>
                            <ng-template #saveTpl>
                                <a (click)="saveEdit(data.id)">保存</a>
                                <a nz-popconfirm nzTitle="确定取消吗?"   (nzOnConfirm)="cancelEdit(data.id)">取消</a>
                            </ng-template>
                        </div>
                    </td>
                </tr>
            </tbody>
        </nz-table>
        <ng-template #totalTemplate let-total>共 {{ groupListTotal }} 条</ng-template>
    </div>

    <div class="user" *ngIf="selectedIndex === 1">
        <nz-table
            #basicTable
            nzShowSizeChanger
            [nzData]="usersListOfData"
            [nzFrontPagination]="false"
            nzShowPagination
            [nzTotal]="UserListTotal"
            [nzShowTotal]="totalTemplate"
            [nzPageIndex]="userListParams.pageNum"
            [nzPageSize]="userListParams.pageSize"
            [nzBordered]="false"
            [nzPageSizeOptions]="[10, 20, 50, 100]"
            [nzShowQuickJumper]="false"
            (nzPageIndexChange)="pageSizeChangedUser($event)"
            (nzPageSizeChange)="pageChangedUser($event)"
        >
            <thead>
                <tr>
                    <th>成员名称</th>
                    <th>成员ID</th>
                    <th>近期活跃时间</th>
                    <th>
                        成员标签
                        <span class="tool-tip-wrap" style="font-size: 14px; vertical-align: 2px; cursor: pointer; margin-left: 3px">
                            <i nz-icon nzType="question-circle" nzTheme="outline" nz-tooltip nzPlacement="right" [nzTitle]="titleTemplate" [nzTrigger]="'hover'"></i>
                        </span>
                        <ng-template #titleTemplate>
                            <span>输入成员标签，用英文逗号分隔</span>
                        </ng-template>
                    </th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let data of basicTable.data">
                    <td>{{ data.member_name }}</td>

                    <td>{{ data.member_id }}</td>
                    <td>{{ data.last_seen }}</td>

                    <td>
                        <ng-container *ngIf="!editUserCache[data.id].edit; else labelInputTpl">
                            <nz-tag *ngFor="let tag of data.member_label" nz-tooltip [nzTitle]="'成员标签：' + tag">{{ tag }}</nz-tag>
                        </ng-container>
                        <ng-template #labelInputTpl>
                            <input type="text" placeholder="输入成员标签，用逗号分隔" nz-input [(ngModel)]="editUserCache[data.id].data.member_label" />
                        </ng-template>
                    </td>
                    <td>
                        <ng-container *ngIf="!editUserCache[data.id].edit; else saveTpl">
                            <a (click)="showDetailPersonnelDetails(data)">查看人员详情</a>
                            <nz-divider nzType="vertical"></nz-divider>
                            <a (click)="editLabel(data)">编辑标签</a>
                        </ng-container>
                        <ng-template #saveTpl>
                            <a (click)="saveEditUser(data.id)" style="margin-right: 5px">保存</a>
                            <a nz-popconfirm nzTitle="确定取消吗?" (nzOnConfirm)="cancelEditUser(data.id)">取消</a>
                        </ng-template>
                    </td>
                </tr>
            </tbody>
        </nz-table>
        <ng-template #totalTemplate let-total>共 {{ UserListTotal }} 条</ng-template>
    </div>
</div>
