import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root'
})
export class ClueMappingService {

    constructor() { }
    // 木马攻击索引
    trojan_attack_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',
        'malware_name': '恶意程序名称',
        'malware_function': '恶意程序功能',
        'compromised_ip': '本地失陷IP地址',
        'compromised_ip_affiliated_organization': '本地失陷IP归属单位',
        'compromised_ip_communication_time': '本地失陷IP通信时间',

        'callback_ip': '回连IP',
        'callback_domain': '回连域名',
        'callback_domain_registration_time': '回连域名注册时间',
        'callback_domain_provider': '回连域名服务商',
        'callback_domain_registrant': '回连域名注册人',
        'callback_domain_registration_email': '回连域名注册邮箱',
        'callback_domain_registration_phone': '回连域名注册电话',
        'callback_domain_resolved_ip': '回连域名解析IP',
        'callback_domain_resolved_ip_location': '回连域名解析IP地理位置',
        'judgement_hacker_group': '研判原因黑客组织',
        'judgement_hacker_group_background': '研判原因黑客组织背景',
        'judgement_associated_features': '研判原因关联特征',
        'judgement_other': '研判原因其他',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        'suggestions': '工作建议',
        // 'raw_id': '是否云端数据'
    }
    // 钓鱼网站索引
    fishing_website_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',
        'spoofed_company': '被仿冒网站单位',
        'spoofed_system': '被仿冒网站系统',
        'spoofed_webpage': '被仿冒网站页面',
        'phishing_link': '钓鱼网站链接',
        'phishing_behavior': '钓鱼网站行为',
        'phishing_registration_time': '钓鱼网站注册时间',
        'phishing_provider': '钓鱼网站服务商',
        'phishing_registrant': '钓鱼网站注册人',
        'phishing_registration_email': '钓鱼网站注册邮箱',
        'phishing_registration_phone': '钓鱼网站注册电话',
        'phishing_resolved_ip': '钓鱼网站解析IP',
        'phishing_resolved_ip_location': '钓鱼网站解析IP地理位置',
        'judgement_hacker_group': '研判原因黑客组织',
        'judgement_hacker_group_background': '研判原因黑客组织背景',
        'judgement_comment_language': '研判原因注释语言',
        'judgement_other': '研判原因其他',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'



    }
    // 钓鱼邮件索引
    fishing_email_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',

        'recipient_company': '收件单位',
        'recipient_email': '收件邮件',
        'email_subject': '邮件主题',
        'email_content': '邮件内容',
        'behavior_phishing_link': '恶意行为钓鱼链接',
        'behavior_spoofed_system': '恶意行为仿冒系统',
        'behavior_malware_name': '恶意行为恶意程序名',
        'behavior_malware_function': '恶意行为恶意程序功能',
        'behavior_callback_domain': '恶意行为回连域名',
        'behavior_other': '恶意行为其他',
        'phishing_registration_time': '钓鱼域名注册时间',
        'phishing_provider': '钓鱼域名服务商',
        'phishing_registrant': '钓鱼域名注册人',
        'phishing_registration_email': '钓鱼域名注册邮箱',
        'phishing_registration_phone': '钓鱼域名注册电话',
        'phishing_resolved_ip': '钓鱼域名解析IP',
        'phishing_resolved_ip_location': '钓鱼域名解析IP地理位置',
        'judgement_hacker_group': '研判原因黑客组织',
        'judgement_hacker_group_background': '研判原因黑客组织背景',
        'judgement_comment_language': '研判原因注释语言',
        'judgement_other': '研判原因其他',
        'phishing_received_company': '收文单位',
        'phishing_received_person': '收文人员',
        'phishing_received_email': '收文邮件',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'
    }
    // 水坑攻击索引
    water_attack_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',
        'compromised_website_url': '被控网站网址',
        'compromised_website_name': '被控网站名称',
        'compromised_website_company': '被控网站责任单位',
        'compromised_website_purpose': '被控网站用途',
        'compromised_website_users': '被控网站主要用户',
        'attack_process_malware_name': '攻击过程恶意程序名',
        'attack_process_malware_function': '攻击过程恶意程序功能',
        'attack_process_callback_domain': '攻击过程回连域名',
        'attack_process_callback_ip': '攻击过程回连IP',
        'attack_process_other': '攻击过程其他',
        'compromised_domain_registration_time': '被控域名注册时间',
        'compromised_domain_provider': '被控域名服务商',
        'compromised_domain_registrant': '被控域名注册人',
        'compromised_domain_registration_email': '被控网站注册邮箱',
        'compromised_domain_registration_phone': '被控域名注册电话',
        'compromised_domain_resolved_ip': '被控域名解析IP',
        'compromised_domain_resolved_ip_location': '被控域名解析IP地理位置',
        'callback_domain': '回连域名',
        'callback_domain_registration_time': '回连域名注册时间',
        'callback_domain_provider:': '回连域名服务商',
        'callback_domain_registrant': '回连域名注册人',
        'callback_domain_registration_email': '回连域名注册邮箱',
        'callback_domain_registration_phone': '回连域名注册电话',
        'callback_domain_resolved_ip': '回连域名解析IP',
        'callback_domain_resolved_ip_location': '回连域名解析IP地理位置',
        'judgement_hacker_group': '研判原因黑客组织',
        'judgement_hacker_group_background': '研判原因黑客组织背景',
        'judgement_other': '研判原因其他',
        'target_audience': '目标群体',
        'compromised_company': '中招单位',
        'compromised_ip': '中招IP',
        'compromised_time': '中招时间',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'


    }
    // 爆破扫描索引
    brute_scan_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',
        'victim_time': '受害时间',
        'victim_company': '受害单位',
        'attack_source_count': '攻击源数量',
        'attack_count': '攻击次数',
        'attack_method': '攻击方式',
        'judgement_domestic_ip': '研判原因境内IP',
        'judgement_domestic_ip_user': '研判原因境内IP使用人',
        'judgement_hacker_group': '研判原因黑客组织',
        'judgement_hacker_group_background': '研判原因黑客组织背景',
        'judgement_other': '研判原因其他',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'




    }
    // 僵尸网络索引
    zombie_network_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',
        'victim_company': '受害单位',
        'victim_device': '受害设备',
        'botnet_virus_name': '僵尸病毒名称',
        'virus_activity_callback_address': '病毒活动情况回连地址',
        'virus_activity_callback_ip': '病毒活动情况回连IP',
        'virus_activity_callback_ip_location': '病毒活动情况回连IP地理位置',
        'virus_activity_other': '病毒活动情况其他',
        'virus_introduction_first_seen': '病毒介绍最早出现时间',
        'virus_introduction_creating_group': '病毒介绍制作团伙',
        'virus_introduction_domestic_infection_count': '病毒介绍境内感染数',
        'virus_introduction_other': '病毒介绍其他',
        'judgement_entry_cause': '研判原因传入原因',
        'judgement_infection_count': '研判原因感染数',
        'judgement_other': '研判原因其他',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'

    }
    // 蠕虫病毒索引
    worm_virus_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',

        'victim_company': '受害单位',
        'victim_ip': '受害IP',
        'virus_name': '病毒名称',
        'virus_activity': '病毒活动情况',
        'virus_introduction_first_seen': '病毒介绍最早出现时间',
        'virus_introduction_creating_group': '病毒介绍制作团队',
        'virus_introduction_domestic_infection_count': '病毒介绍境内感染数',
        'virus_introduction_other': '病毒介绍其他',
        'judgement_entry_cause': '研判原因传入原因',
        'judgement_infection_count': '研判原因感染数',
        'judgement_other': '研判原因其他',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'


    }
    // 网页篡改索引
    web_modification_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',
        'victim_time': '受害时间',
        'victim_website': '受害网站名',
        'victim_url': '受害网址',
        'tampered_content': '纂改内容',
        'hacker_alias': '黑客自称',
        'victim_company': '受害者介绍受害单位',
        'victim_website_purpose': '受害者介绍网站用途',
        'victim_website_users': '受害者介绍网站用户',
        'attack_method_vulnerability': '攻击手法漏洞',
        'attack_method_other': '攻击手法其他',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'

    }
    // 数据泄露索引
    data_leak_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',
        'publish_time': '发布时间',
        'hacker_nickname': '黑客昵称',
        'hacker_forum': '黑客论坛',
        'publish_link': '发布链接',
        'post_victim_company': '贴文内容受害单位',
        'post_data_name': '贴文内容数据名称',
        'post_data_content': '贴文内容数据内容',
        'post_intrusion_method': '贴文内容入侵方式',
        'post_other': '贴文内容其他',
        'judgement_data_leak_company': '研判数据泄露来源单位',
        'judgement_data_leak_system': '研判数据泄露来源系统',
        'judgement_data_leak_source_data_name': '研判数据泄露系统数据名称',
        'judgement_data_leak_source_other': '研判数据泄露来源其他',
        'judgement_data_leak_attack_method_vul': '研判数据泄露攻击手法漏洞',
        'judgement_data_leak_attack_method_other': '研判数据泄露攻击手法其他',
        'judgement_data_leak_risk': '研判数据泄露风险',
        'judgement_code_leak_source_company': '研判代码泄露来源单位',
        'judgement_code_leak_source_system': '研判代码泄露来源系统',
        'judgement_code_leak_source_system_purpose': '研判代码泄露来源系统用途',
        'judgement_code_leak_source_development_company': '研判代码泄露来源开发公司',
        'judgement_code_leak_source_other': '研判代码泄露来源其他',
        'judgement_code_leak_attack_method_vul': '研判代码泄露攻击手法漏洞',
        'judgement_code_leak_attack_method_other': '研判代码泄露攻击手法其他',
        'judgement_code_leak_risk': '研判代码泄露风险',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',

        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'



    }
    // 特定漏洞索引
    specific_vulnerability_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',
        'company_name': '公司名称',
        'system_name': '系统名称',
        'system_link': '系统链接',
        'vulnerability_name': '漏洞名称',
        'vulnerability_cause': '漏洞成因',
        'vulnerability_impact': '漏洞影响',
        'company_founding_time': '公司情况成立时间',
        'company_representative': '公司情况法人',
        'company_business': '公司情况从事业务',
        'company_main_customers': '公司情况主要客户',
        'company_other': '公司情况其他',
        'system_purpose': '系统用途',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',

        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'




    }
    // 通用漏洞索引
    general_vulnerability_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',

        'company_name': '公司名称',
        'system_name': '系统名称',
        'vulnerability_name': '漏洞名称',
        'vulnerability_exploitation_method': '漏洞利用方式',
        'vulnerability_impact': '漏洞影响',
        'development_company': '研发公司',
        'development_company_business': '研发公司从事业务',
        'development_company_system_function': '研发公司系统功能',
        'system_usage_purchase_count': '系统使用情况采购数',
        'system_usage_main_customers': '系统使用情况主要客户',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'



    }

    // 黑客团伙索引
    hacker_team_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',

        'region': '地区',
        'name': '姓名',
        'forum': '平台',
        'content': '发布内容',
        'illegal_activity_fact': '违法活动事实',
        'suggestions': '工作建议',
        'evidence_files': '佐证文档',
        'evidence_photos': '佐证图片',
        'reserved_field_1': '预留字段1',
        'reserved_field_2': '预留字段2',
        'reserved_field_3': '预留字段3',
        'reserved_field_4': '预留字段4',
        'reserved_field_5': '预留字段5',
        'reserved_field_6': '预留字段6',
        'reserved_field_7': '预留字段7',
        'reserved_field_8': '预留字段8',
        'reserved_field_9': '预留字段9',
        'reserved_field_10': '预留字段10',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'


    }
    // 其他攻击索引
    other_attack_zhTen: any = {
        'title': '线索标题',
        'found_time': '发现时间',
        'clue_category': '线索类型',
        'custom_labels': '标签',
        // 'is_delete': '是否删选',
        'judge_state': '研判状态',
        'user_name': '上报人',
        // 'user_group': '上报组',
        'report_time': '上报时间',
        'threat_rating': '威胁评分',
        'detail': '详情信息',
        'description': '线索描述',
        'richness': '线索丰富度',
        'source': '线索来源',
        // 'raw_id': '是否云端数据'

    }

}
