import { Component, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { NzModalService, NzDrawerService } from "ng-zorro-antd";
import { InvoleDatabaseService } from "../../services/invole-database.service";

import { IntelligeceService } from "app/intelligence/service/intelligece.service";
import { Router } from "@angular/router";
import { DataService } from "app/invole/tools/dataServices";
import { intel_Service } from "app/invole/tools/intelService";

import { Subscription } from "rxjs";

import { RoutingHistoryService } from "app/invole/tools/RoutingHistoryService";

// import { NewsCoreService } from "../../services/news-core.service";
import { NewsAttachmentUploadComponent } from "app/invole/components/news-attachment-upload/news-attachment-upload.component";
// import { NewsJudgeTaskCreatedComponent } from "app/invole/components/news-judge-task-created/news-judge-task-created.component";
// import { NewsAttachmentUploadComponent } from "../../components/news-attachment-upload/news-attachment-upload.component";
// import { NewsJudgeTaskCreatedComponent } from "../../components/news-judge-task-created/news-judge-task-created.component";

@Component({
    selector: "app-news-detail",
    templateUrl: "./news-detail.component.html",
    styleUrls: ["./news-detail.component.styl"],
})
export class NewsDetailComponent implements OnInit {
    topic_base_info: any = {};
    related: any = {};
    loading: boolean = true;
    tabsOrder = [
        "file",
        "vulnerability",
        "ipv4",
        "ipv6",
        "domain",
        "url",
        "threat_actor",
        // "email",
        // "attachment",
    ];
    tabs = [];

    fileTableData: any[] = [];
    params = {
        _id: "",
    };
    countStyle = {
        background: "rgba(85, 167, 34, 0.1)",
        color: "#68b92e",
        marginLeft: "10px",
    };

    hashArrayWithKeyValue: { key: string; value: any }[];
    vulnerabilityArray: { key: string; value: any }[];

    constructor(
        private route: ActivatedRoute,
        private service: InvoleDatabaseService,
        private modalService: NzModalService,
        private drawerService: NzDrawerService,
        private intel_s: IntelligeceService,
        private dataService: DataService,
        private intel_Service: intel_Service,
        private router: Router,
        private routingHistoryService: RoutingHistoryService
    ) { }
    previousRoute: string | null = null;

    ngOnInit() {


        this.route.queryParams.subscribe((queryParams) => {
            this.params._id = queryParams["id"];
            // console.log(queryParams["id"], 'queryParams["id"]');

            this.getDetail(this.params);

        });
        this.previousRoute = this.routingHistoryService.getPreviousRoute();

    }

    isObjectNotEmpty(object: any): boolean {
        return object && Object.keys(object).length > 0;
    }

    isObjectEmpty() {
        if (
            Object.values(this.related.file).every(
                (value) => value === null || value === undefined
            ) &&
            Object.values(this.related.vulnerability).every(
                (value) => value === null || value === undefined
            )
        ) {
            this.tabsOrder = this.tabsOrder.filter(
                (tab) => tab !== "file" && tab !== "vulnerability"
            );
        }
    }



    formatData(data) {
        let result = '';
        data.forEach(item => {
            if (item.value !== null && item.value !== undefined && item.value !== "") {
                result += `"${item.key}":"${item.value}",\n`;
            }
        });
        return result.trim(); // 去除末尾的逗号和换行符
    }

    getDetail(params) {
        this.loading = true;
        this.service.getTopicDetail(params).subscribe(
            (resp) => {
                this.loading = false;
                if (resp.code === 200) {
                    if (resp.data.custom_labels != null) {
                        resp.data.custom_labels = resp.data.custom_labels.filter(label => label !== "");
                    }
                    this.topic_base_info = resp.data;
                    this.related = resp.data.ioc;
                    // console.log(this.related.file, "this.related");
                    const data = Object.keys(this.related.file)
                        .map((key) => ({
                            key: key,
                            value: this.related.file[key],
                        }))
                        .filter(
                            (item) =>
                                item.value !== null &&
                                item.value !== undefined &&
                                item.value !== ""
                        );
                    this.hashArrayWithKeyValue = data.reduce((acc, item) => {
                        item.value.forEach(value => {
                            acc.push({
                                key: item.key,
                                value: [value]
                            });
                        });
                        return acc;
                    }, []);
                    const vuln = Object.keys(this.related.vulnerability)
                        .map((key) => ({
                            key: key,
                            value: this.related.vulnerability[key],
                        }))
                        .filter(
                            (item) =>
                                item.value !== null &&
                                item.value !== undefined &&
                                item.value !== ""
                        );

                    this.vulnerabilityArray = vuln.reduce((acc, item) => {
                        item.value.forEach(value => {
                            acc.push({
                                key: item.key,
                                value: [value]
                            });
                        });
                        return acc;
                    }, []);

                    this.tabs = [];
                    this.isObjectEmpty();
                    let addedTabs = new Set<string>();
                    this.tabsOrder.forEach((v) => {
                        const relatedItem = this.related[v];
                        for (let key in relatedItem) {
                            if (this.isObjectNotEmpty(relatedItem[key])) {
                                if (!addedTabs.has(v)) {
                                    this.tabs.push(v);
                                    addedTabs.add(v);
                                }
                            }
                        }
                    });
                }
            },
            (error) => {
                this.loading = false;
            }
        );
    }

    countNonEmptyProperties(obj: any): number {
        let count = 0;

        if (typeof obj === 'object' && obj !== null) {
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    if (Array.isArray(obj[key])) {
                        // 如果是数组，递归计算数组中非空元素的数量
                        count += this.countNonEmptyArrayElements(obj[key]);
                    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                        // 如果是嵌套对象，递归计算对象中非空属性的数量
                        count += this.countNonEmptyProperties(obj[key]);
                    } else if (obj[key] !== null && obj[key] !== undefined) {
                        // 如果是基本类型且非空，计数加一
                        count++;
                    }
                }
            }
        }

        return count;
    }

    countNonEmptyArrayElements(arr: any[]): number {
        let count = 0;
        for (const element of arr) {
            if (element !== null && element !== undefined) {
                // 对于数组中的元素，如果它是对象，则递归计算；否则，直接计数
                if (typeof element === 'object' && element !== null) {
                    count += this.countNonEmptyProperties(element);
                } else {
                    count++;
                }
            }
        }
        return count;
    }

    getRelatedLength(item: string): number {
        return this.countNonEmptyProperties(this.related[item]);
    }

    // 返回
    back() {
        // 从localStorage获取数据
        const data = JSON.parse(localStorage.getItem("detailMessage") || 'null');
        const list = JSON.parse(localStorage.getItem("intelligenceMessage") || 'null');

        // 定义路由与服务的映射关系
        const routeConfig = {
            "/invole/invole-intelligence": {
                path: "/invole/invole-intelligence",
                service: this.intel_Service,
                data: list
            },
            "/invole/invole-intelligence-sw": {
                path: "/invole/invole-intelligence-sw",
                service: this.intel_Service,
                data: list
            },
            "default": {
                path: "/invole/invole-subscribe",
                service: this.dataService,
                data: data
            }
        };

        // 获取当前路由配置，如果没有匹配则使用默认配置
        const config = routeConfig[this.previousRoute as keyof typeof routeConfig] || routeConfig.default;

        // 设置数据并导航
        config.service.setData(config.data);
        this.router.navigate([config.path]);
    }
}
