# 绿盟威胁情报平台(ntip_bsa)

## 首先你需要安装 NodeJS
可以安装nvm管理nodejs的版本 经测试node@14稳定运行 推荐

## 然后安装前端工程环境的依赖
`npm install`  或者是有pnpm yarn 都可以 推荐使用pnpm
> 有问题可以全部删掉node modules文件夹和lock文件 重新安装一遍再试试

安装node@14、angular-cli@7
-- angular-cli 不用全局安装也行 直接使用npx ng 


## 启动本地开发环境

你需要运行 `npm run start --app=ntip_produce`，其中ntip_produce可以是对应BSA app的URL

默认自动打开浏览器，`http://localhost:4200/` 。

可以跟一些ng 自带的参数在后面 比如：`npm run start --app=ntip_manage --port 4400`

## 修改代理的账号密码
当然，你必须要在 `proxy.conf.js` 文件中填写正确的服务端IP、登录用户名密码，以及联调接口地址，才能进行前后端联调。
```
const user = {
    username: 'admin',
    password: '',
};
const protocol = 'http';
const hostname = '';
const port = 4433;
const urls = [ // 接口地址
    '/WebApi',
    '/shield'
];
```

## 代码脚手架

你可以完全手动地创建一个组件、服务或者其它的部件，但是个人还是建议通过如下命令来快速创建部件，它可以自动生成符合规范的代码命名、添加测试代码模板和添加模块引用，尤其对于初学者又很有帮助。
Component(组件): `npm run ng g component ./core/my-new-component`
Directive(指令): `npm run ng g directive ./core/my-new-directive`
Pipe(管道): `npm run ng g pipe ./core/my-new-pipe`
Service(服务): `npm run ng g service ./core/my-new-service`
Class(类): `npm run ng g class ./core/my-new-class`
Interface(接口): `npm run ng g interface ./core/my-new-interface`
Enum(枚举): `npm run ng g enum ./core/my-new-enum`
Module(模块): `npm run ng g module ./core/my-module`

> ps: 可以使用npx 创建，最好指定模块，这样会自动挂载到对应模块，不然没有挂载的打包的时候会有问题

`npx ng g pipe -m news ./news/news.pipe`
`npx ng g component -m news ./news/components/news-judge-task-conclusion`
`npx ng g component -m news ./news/common/news-report-list`

> 按照之前项目的默认约定： 页面组件放到common文件夹 其他组件放到components

## 编译

运行 `npm run build --app=ntip_produce` 编译项目。编译后的代码位于 `ntip_produce_dist/` 文件夹。 --app对应BSA的app路径

## 运行单元测试

运行 `ng test` 执行单元测试，参见 [Karma](https://karma-runner.github.io).

## 运行端对端测试(end-to-end)

运行 `ng e2e` 执行 end-to-end 测试，参见 [Protractor](http://www.protractortest.org/).
测试前请确保已经通过 `ng serve` 命令启动了开发模式。
# 代码依赖


# 部署路径 
- ntip_bsa 为对应app

/home/<USER>/ISOP/apps/ntip_bsa/static  //组件目录自带的
/home/<USER>/ISOP/static/ntip_bsa/static  //执行收集静态资源的命令后生成的 python manage.py collectstatic --noinput  
> ps: 估计是为了目录权限好控制

# BSA 文档
https://secplat.intra.nsfocus.com/pages/viewpage.action?pageId=261718666

前端导航对应的是后端目录的json文件right_config.json
> ps: 注意菜单对应的json文件，key配置了就最好不变化 如果key变了，有些角色的权限可能有问题；如果key改了，角色表里的也得改
