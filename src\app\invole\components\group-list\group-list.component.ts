import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router, NavigationEnd } from "@angular/router";
import { NzTabChangeEvent } from 'ng-zorro-antd/tabs';
import { FormBuilder, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { Observable, Observer } from 'rxjs';
import { NzMessageService, UploadFile } from 'ng-zorro-antd';
import { UtilService } from "../../../shared/common/util.service";
import moment from "moment";


import { IntelligenceTargetService } from '../../services/intelligence-target.service';
@Component({
    selector: 'group-list',
    templateUrl: './group-list.component.html',
    styleUrls: ['./group-list.component.styl']
})
export class GroupListComponent implements OnInit {
    searchText: string = null;
    selectedIndex = 0;

    // 群组列表
    groupsListOfData = [];
    // 用户列表
    usersListOfData = [];
    // tabs = [1, 2, 3];
    pageNumbers = [1, 2, 3, 4, 5];

    editCache: { [key: string]: any } = {};
    editUserCache: { [key: string]: any } = {};
    // 展示群组标签、
    groupTags = []

    // 群组查询弹窗
    isQueryVisible = false;
    // 用户查询弹窗
    isQueryVisibleUser = false;
    packageVisible: boolean = false;


    // // 人员编辑标签model
    // member_labels: string;

    // 群组列表参数
    groupListParams = {
        pageSize: 10,
        pageNum: 1,

    }
    // 用户列表参数
    userListParams = {
        pageSize: 10,
        pageNum: 1,

    }

    // 群组总数
    groupListTotal = 0;
    // 用户列表总数
    UserListTotal = 0;

    // 开始时间
    start_time = null;
    // 结束时间
    end_time = null;

    private navigateToDetail(entity: any, isGroup: boolean): void {
        this.router.navigate(["invole/investigate-detail"], {
            queryParams: {
                ...entity,
                isGroup: isGroup,
                tabsIndex: this.selectedIndex,
            },

        });
    }

    constructor(
        private router: Router,
        private service: IntelligenceTargetService,
        private msg: NzMessageService,
        private route: ActivatedRoute,
        private utilService: UtilService

    ) { }


    ngOnInit(): void {
        this.route.queryParams.subscribe((queryParams) => {
            if (queryParams) {
                this.selectedIndex = parseInt(queryParams.tabsIndex) || 0;
                if (this.selectedIndex === 0) {
                    this.getGroupList();
                } else {
                    this.getUsersList();
                }
            }
        })

    }


    // 群组列表
    getGroupList() {
        this.service.getGroupInformationList(this.groupListParams).subscribe(res => {
            // console.log(res, '=群组列表ssss');
            if (res && res.code === 200) {
                this.groupListTotal = res.data.count;

                // 转换 group_label 字符串为数组
                this.groupsListOfData = res.data.results.map(group => ({
                    ...group,
                    group_label: Array.isArray(group.group_label)
                        ? group.group_label.filter(item => item.trim() !== '')
                        : (group.group_label || "").split(',').filter(item => item.trim() !== '')

                }));
                this.updateEditCache();
            }
        })
    }
    // 用户列表
    getUsersList() {
        this.service.getMemberInformationList(this.userListParams).subscribe(res => {
            // console.log(res, '=用户列表');
            if (res && res.code === 200) {
                this.UserListTotal = res.data.count;
                this.usersListOfData = res.data.results.map(user => ({
                    ...user,
                    member_label: Array.isArray(user.member_label)
                        ? user.member_label.filter(item => item.trim() !== '')
                        : (user.member_label || "").split(',').filter(item => item.trim() !== '')
                }))
                this.updateUserEditCache();
            }
        })
    }



    // 群组查看详情
    showDetailGroupDetails(group: any) {
        this.navigateToDetail(group, true);
    }
    // 用户查看详情
    showDetailPersonnelDetails(user: any) {
        this.navigateToDetail(user, false);
    }
    // 群组编辑标签
    startEdit(data): void {
        this.editCache[data.id].edit = true;
    }
    // 群组保存
    saveEdit(id: number): void {
        this.service.getGroupInformationTagEdit({
            id: id,
            group_label: this.editCache[id].data.group_label,

        }).subscribe(res => {
            if (res && res.code === 200) {
                this.msg.success(res.msg);
                this.getGroupList();
                this.editCache[id].edit = false;
            }
        })
    }

    // 群组编辑标签取消
    cancelEdit(id: number): void {
        const index = this.groupsListOfData.findIndex((item, index) => item.id === id);
        this.editCache[id] = {
            data: { ...this.groupsListOfData[index] },
            edit: false
        };
    }



    // 用户编辑标签
    editLabel(data): void {
        this.editUserCache[data.id].edit = true;
    }
    // 用户编辑标签保存
    saveEditUser(id: number): void {
        this.service.getMemberInformationTagEdit({
            id: id,
            member_label: this.editUserCache[id].data.member_label,
        }).subscribe(res => {
            if (res && res.code === 200) {
                this.editUserCache[id].edit = false;
                this.msg.success(res.msg);
                this.getUsersList();
            }
        })
    }
    // 用户编辑标签取消
    cancelEditUser(id: number): void {
        const index = this.usersListOfData.findIndex((item, index) => item.id === id);
        this.editUserCache[id] = {
            data: { ...this.usersListOfData[index] },
            edit: false
        };
    }


    // 群组更新
    updateEditCache(): void {
        this.groupsListOfData.forEach(item => {
            this.editCache[item.id] = {
                edit: false,
                data: {
                    ...item,
                }
            };
        });
    }
    // 用户更新·
    updateUserEditCache(): void {
        this.usersListOfData.forEach(item => {
            this.editUserCache[item.id] = {
                edit: false,
                data: { ...item }
            };
        });
    }
    // tabs 切换
    onTabChange(event: NzTabChangeEvent) {
        // console.log(event.index, 'index');
        const newIndex = event.index;
        this.selectedIndex = newIndex;
        if (newIndex === 0) {
            this.getGroupList();
        } else {
            this.getUsersList();
        }
    }

    // 群组弹窗取消
    handleCancel(): void {
        this.isQueryVisible = false;
        
    }

    // 用户弹窗取消
    handleCancelUser(): void {
        this.isQueryVisibleUser = false;
    }

    // 群组上传
    handleChange({ file, fileList }: { [key: string]: any }): void {
        const status = file.status;
        if (status !== 'uploading') {
            console.log(file, fileList);
        }
        if (status === 'done') {
            this.msg.success(`${file.name} file uploaded successfully.`);
        } else if (status === 'error') {
            this.msg.error(`${file.name} file upload failed.`);
        }
    }

    // 群组及聊天信息批量查询
    getBatchQuery() {
        this.isQueryVisible = true;
    }
    // 信息批量查询
    getBatchQueryUser() {
        this.isQueryVisibleUser = true;

    }
    // 情报包导入
    showPackageUploadModal() {
        this.packageVisible = true;
    }

    // 数据包导入取消
    handlePackageUploadCancel() {
        // console.log("我是情报包取消");
        this.packageVisible = false;
        this.getGroupList();

    }

    //  人员页码改变的回调
    pageSizeChangedUser(e) {
        this.userListParams.pageNum = e;
        this.getUsersList();
    }
    // 每页条数改变的回调
    pageChangedUser(e) {
        this.userListParams.pageNum = 1;
        this.userListParams.pageSize = e;
        this.getUsersList();
    }

    // 群组页码改变的回调
    pageSizeChanged(e) {
        this.groupListParams.pageNum = e;
        this.getGroupList();
    }
    // 每页条数改变的回调
    pageChanged(e) {
        this.groupListParams.pageNum = 1;
        this.groupListParams.pageSize = e;
        this.getGroupList();
    }
    // 群组点击下载模板
    ModelDownload(event: MouseEvent) {
        event.preventDefault(); // 阻止默认行为
        this.service.getGroupInformationListBatchTemplate().subscribe((blob: Blob) => {
            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `群组模板_${moment().format("YYYY-MM-DD")}.xlsx`;
            document.body.appendChild(a);
            a.click();

            // 清理
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        },
            (error) => {
                console.error('下载失败:', error);

            }
        );
    }

    // 用户点击下载模板
    ModelDownloadUser(event: MouseEvent) {
        event.preventDefault(); // 阻止默认行为
        this.service.getMemberInformationListBatchTemplate().subscribe((blob: Blob) => {
            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `人员模板_${moment().format("YYYY-MM-DD")}.xlsx`;
            document.body.appendChild(a);
            a.click();
            // 清理
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

        }
            ,
            (error) => {
                console.error('下载失败:', error);

            }
        )
    }


    // 群组批量上传
    fileList: UploadFile[] = [];
    fileListMember: UploadFile[] = [];

    isPackageErrorMsgShow = false;
    packageErrorMsg: string[] = [];
    uploading = false;


    beforeUpload = (file: UploadFile): boolean => {

        // console.log(file, 'file');

        const maxFileSize = 100 * 1024 * 1024;
        // const fileExtension = /\.gz$/;
        const fileNamePattern =
            /^[a-zA-Z0-9_]+(?:\.[a-zA-Z0-9\-]+)*\.(?:[a-zA-Z0-9\-]+\.\d{8}\.\d{4})$/;

        // const fileNamePattern =
        //   /^[a-zA-Z0-9_]+(?:\.[a-zA-Z0-9_]+)*\.(?:[a-zA-Z0-9_]+\.\d{8}\.\d{4})$/;

        // const baseName = file.name.split(".").slice(0, -1).join(".");

        if (file.size > maxFileSize) {
            this.msg.error("文件大小不能超过 100MB");
            return false;
        }
        // if (!fileNamePattern.test(baseName)) {
        //     // 注意这里是 !，表示“不匹配”
        //     this.message.error("文件名格式不匹配");
        //     return false;
        // }
        this.packageErrorMsg = [];
        this.isPackageErrorMsgShow = false;
        if (this.selectedIndex === 0) {
            this.fileList = [file];
        } else {
            this.fileListMember = [file];
        }
        return false;
    };


    // 群组批量上传
    handlePackageUpload(): void {
        this.packageErrorMsg = [""];
        this.isPackageErrorMsgShow = false;

        // 检查是否选择了文件
        if (!this.fileList || this.fileList.length === 0) {
            this.msg.error("请先选择Excel文件");
            return;
        }

        const formData = new FormData();
        formData.append("file", this.fileList[0] as any);



        console.log(this.start_time, this.end_time,'时间');
        
        // 添加其他参数
        if (this.start_time) {
            formData.append("start_time", this.start_time.trimEnd());
        }
        if (this.end_time) {
            formData.append("end_time", this.end_time.trimEnd());
        }

        console.log(formData,'formData');
    

        this.uploading = true;

        // 调用服务方法，明确指定响应类型为blob
        this.service.getGroupInformationListBatchQuery(formData).subscribe(
            (response: Blob) => {
                this.uploading = false;

                // 创建下载链接
                const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;

                // 设置下载文件名（可以根据需要自定义）
                const fileName = `查询结果_${new Date().toISOString().slice(0, 10)}.xlsx`;
                a.download = fileName;

                // 触发下载
                document.body.appendChild(a);
                a.click();

                // 清理
                window.URL.revokeObjectURL(downloadUrl);
                document.body.removeChild(a);

                // 重置表单
                this.fileList = [];
                // this.start_time = [];
                // this.end_time = [];
                this.msg.success('Excel文件下载成功');
            },
            (error) => {
                this.uploading = false;

                // 处理错误响应
                if (error.error instanceof Blob) {
                    // 如果错误响应也是Blob（比如服务器返回的错误Excel）
                    const reader = new FileReader();
                    reader.onload = () => {
                        try {
                            const errMsg = JSON.parse(reader.result as string);
                            this.msg.error(errMsg.message || '查询失败');
                        } catch (e) {
                            this.msg.error('查询失败，请检查文件格式');
                        }
                    };
                    reader.readAsText(error.error);
                } else {
                    this.msg.error(error.message || '查询失败');
                }
            }
        );
    }

    // 人员批量上传
    handleMemberPackageUpload(): void {
        this.packageErrorMsg = [""];
        this.isPackageErrorMsgShow = false;

        // 检查是否选择了文件
        if (!this.fileListMember || this.fileListMember.length === 0) {
            this.msg.error("请先选择Excel文件");
            return;
        }

        const formData = new FormData();
        formData.append("file", this.fileListMember[0] as any);

        // // 添加其他参数
        // if (this.start_time) {
        //     formData.append("start_time", this.start_time.trimEnd());
        // }
        // if (this.end_time) {
        //     formData.append("end_time", this.end_time.trimEnd());
        // }

        this.uploading = true;

        // 调用服务方法，明确指定响应类型为blob
        this.service.getMemberInformationListBatchQuery(formData).subscribe(
            (response: Blob) => {
                this.uploading = false;

                // 创建下载链接
                const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;

                // 设置下载文件名（可以根据需要自定义）
                const fileName = `查询结果_${new Date().toISOString().slice(0, 10)}.xlsx`;
                a.download = fileName;

                // 触发下载
                document.body.appendChild(a);
                a.click();

                // 清理
                window.URL.revokeObjectURL(downloadUrl);
                document.body.removeChild(a);

                // 重置表单
                this.fileListMember = [];
                this.start_time = null;
                this.end_time = null;
                this.msg.success('Excel文件下载成功');
            },
            (error) => {
                this.uploading = false;

                // 处理错误响应
                if (error.error instanceof Blob) {
                    const reader = new FileReader();
                    reader.onload = () => {
                        try {
                            const errMsg = JSON.parse(reader.result as string);
                            this.msg.error(errMsg.message || '查询失败');
                        } catch (e) {
                            this.msg.error('查询失败，请检查文件格式');
                        }
                    };
                    reader.readAsText(error.error);
                } else {
                    this.msg.error(error.message || '查询失败');
                }
            }
        );
    }
    onChange(result: Date): void {
        console.log('Selected Time: ', result);
    }
    onOk(result: Date): void {
        if (result[0] && result[1]) {
            this.start_time = moment(result[0]).format("YYYY-MM-DD HH:mm:ss ");
            this.end_time = moment(result[1]).format("YYYY-MM-DD HH:mm:ss");
        } else {
            this.start_time = [];
            this.end_time = [];
        }
    }
}
