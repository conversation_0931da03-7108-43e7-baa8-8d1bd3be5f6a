<div class="asset_library">
    <div class="asset_library__header">
        <div style="display: flex; align-items: center; justify-content: space-between; width: 73%">
            <div class="search">
                <nz-input-group [nzPrefix]="prefixTemplate" [nzAddOnAfterIcon]="'search'">
                    <input type="text" [(ngModel)]="params.keywords" nz-input placeholder="任意关键字检索" />
                </nz-input-group>
                <ng-template #prefixTemplate>
                    <span style="display: block; height: 15px"> * </span>
                </ng-template>
            </div>
            <div class="timer" style="width: calc(100vw * (420 / 1920))">
                <span style="width: 100px">时间筛选：</span>
                <nz-range-picker [(ngModel)]="dateRange" [nzRanges]="ranges" (ngModelChange)="onDateRangeChange($event)"></nz-range-picker>
            </div>
            <div class="btn">
                <button nz-button nzType="primary" (click)="search()">搜索</button>
            </div>
        </div>
    </div>
    <div class="asset_library__content">
        <div class="asset_library__content__tab">
            <nz-table
                #basicTable
                [nzLoading]="WebsiteLoading"
                [nzShowPagination]="true"
                nzShowSizeChanger
                [nzData]="WebsiteTagsList"
                [nzShowTotal]="totalTemplate"
                [nzPageIndex]="params.page_num"
                [nzPageSize]="params.page_size"
                [nzFrontPagination]="false"
                [nzBordered]="false"
                [nzPageSizeOptions]="[10, 20, 30, 50]"
                [nzShowQuickJumper]="true"
                [nzTotal]="total"
                (nzPageIndexChange)="onPageIndexChange($event)"
                (nzPageSizeChange)="onPageSizeChange($event)"
            >
                <thead>
                    <tr>
                        <th>更新时间</th>
                        <th>创建人</th>
                        <th>URL 地址</th>
                        <th>风险类型</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of basicTable.data">
                        <td>{{ data.modified }}</td>
                        <td>{{ data.create_by }}</td>
                        <td>
                            <a [href]="data.object.value" target="_blank"> {{ data.object.value }}</a>
                        </td>
                        <td [innerHTML]="data.type"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #totalTemplate let-total>共 {{ total }} 条</ng-template>
        </div>
    </div>
</div>
