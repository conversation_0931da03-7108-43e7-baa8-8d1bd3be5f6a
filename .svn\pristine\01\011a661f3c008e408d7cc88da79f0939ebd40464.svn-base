import {
    <PERSON>mpo<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ementRef,
    <PERSON>Child,
    HostL<PERSON>ener,
    Inject,
    <PERSON><PERSON><PERSON><PERSON>,
} from "@angular/core";
import { RoutingHistoryService } from "app/invole/tools/RoutingHistoryService";

import { ActivatedRoute, Router, NavigationEnd } from "@angular/router";
import { NzModalRef, NzModalService, NzDrawerService } from "ng-zorro-antd";
import { NzMessageService } from "ng-zorro-antd";
import { UtilService } from "../../../shared/common/util.service";
import { InvoleDatabaseService } from "../../services/invole-database.service";
import moment from "moment";
import { Subscription } from "rxjs";
// import { DataService } from "app/invole/dataServices";

import { intel_Service } from "app/invole/tools/intelService";
// import { intel_Service } from "app/invole/intellgenceServices";
import { NewsSingleEntryComponent } from "../../components/news-single-entry/news-single-entry.component";
@Component({
    selector: "app-invole-intelligence",
    templateUrl: "./invole-intelligence.component.html",
    styleUrls: ["./invole-intelligence.component.styl"],
})
export class InvoleIntelligenceComponent implements OnInit {
    list: any = [];
    public isTableLoading = true;

    // 自定义标签
    // custom_labels: string;
    topicValue: string;
    url: string;
    dateFormat = "yyyy/MM/dd";
    // 涉我情报数量
    // reportsNumber: number ;
    // 关键字
    keyword: string;
    // 单选
    radioValue = "_score";
    // 录入方式
    InputMethod: any = "0";
    // radioValue = "pub_time";

    // 上报人
    user_name: string = null
    // category: string;

    // 添加按钮
    isBtnShow = false;
    isBtnAdd = true;

    goAddGroups() {
        this.isBtnShow = true;
        this.isBtnAdd = false;
    }

    isLoadingSearch = false;
    // 点击搜索
    getSearchValues() {
        this.isLoadingSearch = true;
        this.params.page_num = 1;

        this.params.keywords = this.keyword;
        this.params.topic = this.topicValue;
        this.params.source_url = this.inputValue;
        this.params.custom_label = this.tagsInputValue;
        this.params.user_name = this.user_name;

        // console.log(this.params, " this.params");

        this.getFilterData(this.params);

    }

    // 自定义标签
    customTagsSelectedValue: Array<{ label: string; value: string }> = [];
    // 专题
    specialSelectedValue: Array<{ value: string }> = [];
    //源
    URLSelectedValue: Array<{ label: string; value: string }> = [];

    filter = [
        {
            name: "情报类型：",
            key: "category",
            values: [],
        },
    ];

    params = {
        // 时间范围
        start_time: null,
        end_time: null,
        // 排序
        sort_type: "",
        // 录入方式
        input_method: null,
        // 自定义标签
        custom_label: "",
        // 专题
        topic: "",
        // 源url
        source_url: "",
        //  关键字
        keywords: "",
        page_num: 1,
        page_size: 10,
        // 情报类型
        category: [],
        // 上报人
        user_name: null
    };

    dateRange: Array<Date> = [];
    ranges = {
        今天: [new Date(), new Date()],
        最近七天: [
            moment().subtract(6, "days").startOf("day").toDate(),

            moment().endOf("day").toDate(),
        ],
        最近30天: [
            moment().subtract(29, "days").startOf("day").toDate(),

            moment().endOf("day").toDate(),
        ],
    };
    inputValue: string;
    filteredOptions: any[] = [];
    URLoptions: any[] = [];
    previousRoute: string | null = null;
    private routerEvents: Subscription;

    custom_labels: string;

    selectedIds: Array<string> = [];
    selectedItems: Array<any> = [];
    // 页码总条数
    total: number = 0;
    loading: Boolean = false;
    deleteConfirmModal: NzModalRef;
    uploadVisible: boolean = false;
    packageVisible: boolean = false;
    docsUploadVisible: boolean = false;
    // 单条录入
    SingleEntryVisible: boolean = false;
    // 打标签
    allCustomTags = [];
    selectedOriginalCustomTags: string[] = [];
    customTag: string = "";
    selectedCustomTagItem: any = {};
    // url地址过于长
    urls: { full: string; short: string }[] = [];
    constructor(
        private route: ActivatedRoute,

        private router: Router,
        // private location: Location,
        private modalService: NzModalService,
        private drawerService: NzDrawerService,
        private message: NzMessageService,
        private utilService: UtilService,
        private dataService: intel_Service,
        private service: InvoleDatabaseService,
        private routingHistoryService: RoutingHistoryService,
        private intel_Service: intel_Service
    ) { }

    private routerEventsSubscription: Subscription;

    ngOnInit() {
        document.getElementById("layout").scrollTop = 0;

        this.previousRoute = this.routingHistoryService.getRoute();
        this.routerEvents = this.dataService.data$.subscribe((data) => {

            if (data) {
                this.params = data;
                this.tagsInputValue = data.custom_label || null;
                this.topicValue = data.topic || null;
                this.keyword = data.keywords || null;
                this.inputValue = data.source_url || null;
                this.radioValue = data.sort_type || "_score";
                if (data.input_method != null) this.InputMethod = '' + data.input_method || "0";

                if (data.start_time && data.end_time) {
                    const startTime = new Date(data.start_time);
                    const endTime = new Date(data.end_time);
                    if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
                        this.dateRange = [startTime, endTime];
                    } else {
                        this.dateRange = [];
                    }
                }
            }
        });

        // 订阅路由的NavigationEnd事件
        this.routerEventsSubscription = this.router.events.subscribe((event) => {
            if (event instanceof NavigationEnd) {
                const currentUrl = event.url.slice(0, 19);
                console.log(currentUrl, '情报库');

                if (
                    currentUrl !== "/invole/news-detail" &&
                    currentUrl !== "/invole/invole-inte"
                ) {
                    this.intel_Service.clearData();
                }
            }
        });
        this.getFilterData(this.params);

        this.getTypeTags();

        this.getTopicOptions();
    }

    ngOnDestroy() {
        // this.routerEventsSubscription.unsubscribe();
        this.routerEvents.unsubscribe();
    }

    //TODO:
    // 编辑标签
    addTagClick(item) {
        this.selectedCustomTagItem = item;
        // console.log(item.custom_labels, 'item.custom_labels');

        if (item.custom_labels) {
            this.selectedOriginalCustomTags = [...item.custom_labels];
        }
    }
    addTagToNews() {

        const inputText = this.customTag.trim();

        if (inputText === "") {
            this.message.info("新建标签不能为空");
            this.customTag = "";
            return;
        }

        if (this.selectedCustomTagItem.custom_labels) {
            if (!this.selectedCustomTagItem.custom_labels.includes(inputText)) {
                // 如果不存在，则添加到数组中
                this.selectedCustomTagItem.custom_labels.push(inputText);
            } else {
                this.message.info("新建标签不能重复");
                this.customTag = "";

                return;
            }
        } else {
            this.selectedCustomTagItem.custom_labels = [this.customTag];
        }
        this.service.singleUpdate(this.selectedCustomTagItem).subscribe(
            (resp) => {
                if (resp.code === 200) {
                    this.message.success(resp.msg);
                    this.customTag = "";
                } else {
                    this.message.error("编辑标签失败：" + resp.message);
                    this.selectedCustomTagItem.custom_labels = this.selectedCustomTagItem;
                }
            },
            (error) => {
                this.message.error("编辑标签异常");
            }
        );
    }

    // 情报类型
    getTypeTags() {
        this.service.getIntelligenceType().subscribe((resp) => {
            if (resp.code === 200) {
                resp.data.forEach((i: any) => {
                    this.filter[0].values.push({
                        label: i.label,
                        value: i.value,
                    });
                });
            }
        });
    }

    isAutocompleteVisible = true;
    // 源ulr  todo:
    filterOptions() {
        // console.log(this.inputValue, "this.inputValue");

        this.service
            .getSourceUrl({
                source_url: this.inputValue,
            })
            .subscribe(
                (resp) => {
                    this.URLoptions = [];

                    const uniqueURLs = new Set();
                    if (resp.code === 200 && resp.data.length != 0) {
                        resp.data.forEach((item) => {
                            uniqueURLs.add(item.source_url);
                        });

                        this.URLoptions = Array.from(uniqueURLs);
                    }
                    const searchTerm = this.inputValue.toLowerCase();
                    this.filteredOptions = this.URLoptions.filter((option) => {
                        return option.toLowerCase().includes(searchTerm);
                    });

                    this.params.source_url = this.inputValue;
                },
                (error) => {
                    // console.error("获取自定义标签失败:", error);
                }
            );
    }
    // 自定义标签
    tagsInputValue: string;
    tagsFilteredOptions: string[] = [];
    tagsOptions: string[] = [];

    tagsFilterOptions() {
        // console.log(this.tagsInputValue, "this.tagsInputValue");

        this.service
            .getCustomLabels({
                custom_labels: this.tagsInputValue,
            })
            .subscribe(
                (resp) => {
                    // console.log(resp, "----自定义标签");
                    if (resp.code === 200 && resp.data.length != 0) {
                        this.tagsOptions = [...resp.data];
                    }
                    this.params.custom_label = this.tagsInputValue;

                    const searchTerm = this.tagsInputValue.toLowerCase();
                    this.tagsFilteredOptions = this.tagsOptions.filter((subarray) =>
                        subarray.toLowerCase().includes(searchTerm)
                    );
                },
                (error) => {
                    console.error("获取自定义标签失败:", error);
                }
            );
    }

    // 专题
    getTopicOptions() {
        this.service.getTopicName().subscribe((resp) => {
            // console.log(resp, "---专题");
            if (resp.code === 200) {
                resp.data.forEach((item: any) => {
                    this.specialSelectedValue.push({
                        value: item.topic_name,
                    });
                });
            }
        });
    }

    // 单选
    getSortOptions(value: string) {
        this.params.sort_type = value;
    }

    // 录入条件方式单选
    getSortEnterOptions(value) {
        // console.log(value, "录入条件方式单选");
        this.params.input_method = Number(value);
    }

    // createCustomTag() {
    //   // this.service.createCustomLabel({ new_label: this.customTag }).subscribe(
    //   //   (resp) => {
    //   //     if (resp.status === 2000) {
    //   //       this.message.success("创建成功！");
    //   //       this.allCustomTags.push(this.customTag);
    //   //       this.customTag = "";
    //   //     } else {
    //   //       this.message.error("创建失败：" + resp.message);
    //   //     }
    //   //   },
    //   //   (error) => {
    //   //     this.message.error("创建异常：" + error);
    //   //   }
    //   // );
    // }
    handleCustomTagsChange(checked: boolean, tag: string): void {
        if (checked) {
            this.selectedCustomTagItem.custom_labels.push(tag);
        } else {
            this.selectedCustomTagItem.custom_labels =
                this.selectedCustomTagItem.custom_labels.filter((t) => t !== tag);
        }
    }
    // 点击标签
    tagCheckedClick(type: any, value: any) {
        let arr = this.params[type];
        const index = arr.indexOf(value);
        if (index !== -1) {
            arr.splice(index, 1);
        } else {
            arr.push(value);
        }
        this.params[type] = arr;

        // this.getFilterData(this.params);
    }

    // // 源
    // getOnChangeSourceUrl() {
    //   this.params.source_url = this.inputValue;
    // }

    onChange(result: Date): void {
        console.log("Selected Time: ", result);
    }
    // TODO: 时间转换
    onDateRangeChange(e) {
        // console.log("----onDateRangeChange----", e, this.dateRange);
        if (this.dateRange.length && this.dateRange[0] && this.dateRange[1]) {
            this.params.start_time = moment(this.dateRange[0]).format("YYYY-MM-DD");
            this.params.end_time = moment(this.dateRange[1]).format("YYYY-MM-DD");
            // console.log(this.params.start_time, "this.params.start_date");
            // console.log(this.params.end_time, "this.params.end_date");
        } else {
            this.params.start_time = null;
            this.params.end_time = null;
        }
    }

    orderChanged(e) {
        // this.getList(this.params);
    }
    // 页码改变的回调
    pageChanged(e) {
        this.params.page_num = e;
        this.getFilterData(this.params);
    }
    // 每页条数改变的回调
    pageSizeChanged(e) {
        this.params.page_num = 1;
        this.params.page_size = e;
        this.getFilterData(this.params);
    }
    transformParams(params) {
        let result = { ...params };

        result.order = [{ field: result.order, order: "desc" }];
        // if (this.dateRange.length && this.dateRange[0] && this.dateRange[1]) {
        //   result.start_date = moment(this.dateRange[0]).format('YYYY-MM-DD')
        //   result.end_date = moment(this.dateRange[1]).format('YYYY-MM-DD')
        // }
        return result;
    }
    //TODO:
    initParams(queryParams) {
        this.params.keywords = queryParams.keywords || "";
        this.params.page_num = Number(queryParams.page) || 1;
        this.params.page_size = Number(queryParams.size) || 10;
        // this.params.order = queryParams.order || "created_time";

        if (queryParams.start_date && queryParams.end_date) {
            this.params.start_time = queryParams.start_date;
            this.params.end_time = queryParams.end_date;
            this.dateRange = [
                new Date(queryParams.start_time),
                new Date(queryParams.end_time),
            ];
        }
        const arrayKeys = [
            "category",
            // "property",
            // "gainsource",
            "custom_label",
            // "others",
        ];
        arrayKeys.map((v) => {
            if (queryParams[v] && queryParams[v].length) {
                this.params[v] = queryParams[v].split(",");
            } else {
                this.params[v] = [];
            }
        });
    }
    navigate(params) {
        let queryParams: any = {};

        params.keywords && (queryParams.keywords = params.keywords);
        params.page && params.page != 1 && (queryParams.page = params.page);
        params.size && params.size != 10 && (queryParams.size = params.size);
        params.order &&
            params.order != "created_time" &&
            (queryParams.order = params.order);
        params.start_date && (queryParams.start_date = params.start_date);
        params.end_date && (queryParams.end_date = params.end_date);
        const arrayKeys = [
            "category",
            "property",
            "gainsource",
            "custom_label",
            "others",
        ];
        arrayKeys.map((v) => {
            if (params[v] && params[v].length) {
                queryParams[v] = params[v].join(",");
            }
        });
        // 使用 router.navigate() 方法更新 URL
        this.router.navigate(["news/list"], { queryParams });
        // this.location.search(JSON.stringify(queryParams));
    }

    // 情报类型、自定义标签、其他标签数据
    getFilterData(params = {}) {
        this.loading = true;

        this.isTableLoading = true;

        // console.log(params, "我是获取的参数有");

        this.service.getDatabaseList(params).subscribe(
            (resp) => {
                // console.log(resp, "----数据数据");
                this.isTableLoading = false;

                if (resp.code === 200) {
                    this.loading = true;
                    this.isLoadingSearch = false;
                    // 过滤掉 custom_labels 为空的项
                    resp.data.results.forEach(item => {
                        if (item.custom_labels != null) {
                            item.custom_labels = item.custom_labels.filter(label => label !== "");

                        }

                    });

                    this.list = resp.data.results;
                    // console.log(this.list, 'list');
                    this.total = resp.data.count;
                }
            },
            (error) => {
                this.loading = true;
                this.isTableLoading = false;

                // console.log("-getList-error-", error);
            }
        );
    }

    private intervalId;
    // 添加到批量操作列表
    toggle2BatchItem(item: any) {
        let index = this.selectedIds.indexOf(item._id);
        // 清除之前的定时器（如果有的话）
        if (this.intervalId !== null) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }

        if (index !== -1) {
            this.intervalId = setInterval(() => {
                this.selectedIds.splice(index, 1);
                this.selectedItems.splice(index, 1);

                // 清除定时器
                clearInterval(this.intervalId);
                this.intervalId = null;
            }, 300);
        } else {
            this.intervalId = setInterval(() => {
                this.selectedIds.push(item._id);
                this.selectedItems.push(item);
                // 清除定时器
                clearInterval(this.intervalId);
                this.intervalId = null;
            }, 300);
        }
    }

    showBatchDeleteModal() {
        this.deleteConfirmModal = this.modalService.confirm({
            nzTitle: `确定删除吗?`,
            nzContent: `确定要删除已经选择的${this.selectedIds.length}条数据，请点击确定按钮。`,
            nzOnOk: () =>
                new Promise((resolve, reject) => {
                    this.service.deleteInformation({ _id: this.selectedIds }).subscribe(
                        (resp) => {
                            if (resp.code === 200) {
                                resolve(true);
                                this.message.create("success", "删除成功");
                                this.selectedIds = [];
                                this.selectedItems = [];
                                this.params.page_num = 1;

                                // console.log(this.params, "----=删除");

                                this.getFilterData(this.params);
                            } else {
                                this.message.create(
                                    "error",
                                    resp.msg || "删除失败！请稍后重试。"
                                );
                                reject(false);
                            }
                        },
                        (error) => {
                            this.message.create(
                                "error",
                                error.msg || "请求异常！请稍后重试。"
                            );
                            reject(false);
                        }
                    );
                }).catch((err) => {
                    return false;
                }),
        });
    }
    showBatchExportModal() {
        // console.log("--batchExport-", this.selectedIds);
        this.service.exportInformation({ _id: this.selectedIds }).subscribe(
            (resp) => {
                // console.log(resp, "=-----我是导出");

                // if (resp.code === 200) {
                this.selectedItems = [];
                this.utilService.exportFile(
                    `数据_${moment().format("YYYY-MM-DD HH:mm:ss")}`,
                    resp as Blob,
                    "csv"
                );
                // }
            },
            (error) => {
                this.message.error("导出失败，请稍后重试");
            }
        );
    }
    // 单条录入
    // showSingleEntryModal() {
    //   this.SingleEntryVisible = true;
    // }
    handleEntryCancel() {
        this.SingleEntryVisible = false;
    }
    // 批量录入
    showBatchUploadModal() {
        this.uploadVisible = true;
    }

    // 情报包导入
    showPackageUploadModal() {
        this.packageVisible = true;
    }

    handleUploadCancel() {
        // console.log("我是取消");

        this.uploadVisible = false;
        // this.getFilterData(this.params);
        this.intel_Service.clearData();


    }

    handlePackageUploadCancel() {
        // console.log("我是情报包取消");

        this.packageVisible = false;
        // this.getFilterData(this.params);
        this.intel_Service.clearData();

    }

    // 文档录入
    // showDocsUploadModal() {
    //   this.docsUploadVisible = true;
    // }
    // handleDocsUploadCancel() {
    //   this.docsUploadVisible = false;
    // }
    // showJudgeDrawer(item) {
    //     const drawerRef = this.drawerService.create({
    //         nzTitle: '发起资讯研判',
    //         nzWidth: 640,
    //         nzMaskClosable: false,
    //         // nzContent: NewsJudgeTaskCreatedComponent,
    //         nzContentParams: {
    //             newsItem: item,
    //         }
    //     });

    // }
    // 单条录入
    showSingleEntryModal(): void {
        const modalService = this.modalService.create({
            nzTitle: "录入涉我情报",
            nzWidth: 800,
            nzFooter: null,
            nzMaskClosable: false,
            nzContent: NewsSingleEntryComponent,
            nzComponentParams: {},
        });

        modalService.afterClose.subscribe((result) => {
            this.params.page_num = 1;
            result === true && this.getFilterData(this.params);
        });
    }
    // 编辑资讯
    showEditModal(item): void {
        let modalService = null;
        this.service.getTopicDetail({ _id: item._id }).subscribe(
            (resp) => {
                modalService = this.modalService.create({
                    nzTitle: "编辑涉我情报",
                    nzWidth: 800,
                    nzFooter: null,
                    nzMaskClosable: false,
                    nzContent: NewsSingleEntryComponent,
                    nzComponentParams: {
                        item: resp.data || null,
                    },
                });

                // Return a result when closed
                modalService.afterClose.subscribe((result) => {
                    result === true && this.getFilterData(this.params);
                });
            },
            (error) => {
                this.message.error("获取资讯信息异常，请重试！");
            }
        );
    }
    showCreateReportModal() {
        // console.log("去报告生成页面");

        this.service
            .addListToReports({
                _id: this.selectedIds,
            })
            .subscribe((resp) => {
                if (resp.code === 200) {
                    this.message.success(resp.msg);
                    this.router.navigate(["invole/invole-report"]);
                    this.selectedIds = [];
                    this.selectedItems = [];
                }
            });
    }
}
