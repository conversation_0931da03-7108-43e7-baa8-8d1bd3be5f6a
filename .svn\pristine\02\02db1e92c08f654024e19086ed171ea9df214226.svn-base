<div style="background-color: #fff;padding: 12px;overflow: hidden;">
  <app-card-light [cardTitle]="'贡献排行榜'">
    <div class="list-wrap">
      <div class="gutter-example">
        <div nz-row nzGutter="16" style="display: flex; flex-wrap: wrap;">
          <!-- 综合排名 -->
          <div nz-col class="gutter-row" nzSpan="12" style="display: flex; align-items: stretch; margin-bottom: 24px;">
            <nz-card [nzTitle]="titleTemplate1" style="width: 100%;">
              <ng-template #titleTemplate1>
                <div class="title-container">
                  <span>报告综合排行</span>
                  <nz-select class="statistics_select" [(ngModel)]="ZHtiConsumeTime"
                    (ngModelChange)="ZHtiConsumeTimeChange()" nzDropdownMatchSelectWidth="false"
                    [nzDropdownStyle]="{ 'minWidth': '120px' }">
                    <nz-option *ngFor="let option of granularityOptions" [nzValue]="option.value"
                      [nzLabel]="option.text">
                    </nz-option>
                  </nz-select>
                </div>
              </ng-template>
              <nz-table #ajaxTable nzShowSizeChanger [nzFrontPagination]="false" [nzData]="ZHListOfData"
                [nzLoading]="ZHLoading" [nzFrontPagination]="false" [nzShowPagination]="false">
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>地区</th>
                    <th>报告数量</th>
                    <th>采编数量</th>
                    <th>分数</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of ajaxTable.data; index as i">
                    <td>
                      <span class="ranking"
                        [ngClass]="{'first': ((pageIndex-1)*pageSize + (i+1)) == 1, 'second': ((pageIndex-1)*pageSize + (i+1)) == 2, 'third': ((pageIndex-1)*pageSize + (i+1)) == 3}">
                        {{ (pageIndex-1)*pageSize + (i+1) }}
                      </span>
                    </td>
                    <td style="height: 65px">{{ getCityLabel(data?.city) }}</td>
                    <td style="height: 65px">{{ data?.count }}</td>
                    <td style="height: 65px">{{ data?.adport_count }}</td>
                    <td style="height: 65px">{{ data?.score }}</td>
                  </tr>
                </tbody>
              </nz-table>
            </nz-card>
          </div>

          <!-- 8类情报报告排行 -->
          <div nz-col class="gutter-row" nzSpan="12" style="display: flex; align-items: stretch; margin-bottom: 24px;">
            <nz-card [nzTitle]="titleTemplate2" style="width: 100%;">
              <ng-template #titleTemplate2>
                <div class="title-container">
                  <span>8类情报报告排行</span>
                  <nz-select class="statistics_select" [(ngModel)]="QBtiConsumeTime"
                    (ngModelChange)="QBtiConsumeTimeChange()" nzDropdownMatchSelectWidth="false"
                    [nzDropdownStyle]="{ 'minWidth': '120px' }">
                    <nz-option *ngFor="let option of granularityOptions" [nzValue]="option.value"
                      [nzLabel]="option.text">
                    </nz-option>
                  </nz-select>
                </div>
              </ng-template>
              <nz-table #ajaxTable1 nzShowSizeChanger [nzFrontPagination]="false" [nzData]="QBListOfData"
                [nzLoading]="QBLoading" [nzFrontPagination]="false" [nzShowPagination]="false">
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>地区</th>
                    <th>报告数量</th>
                    <th>采编数量</th>
                    <th>分数</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of ajaxTable1.data; index as i">
                    <td>
                      <span class="ranking"
                        [ngClass]="{'first': ((pageIndex-1)*pageSize + (i+1)) == 1, 'second': ((pageIndex-1)*pageSize + (i+1)) == 2, 'third': ((pageIndex-1)*pageSize + (i+1)) == 3}">
                        {{ (pageIndex-1)*pageSize + (i+1) }}
                      </span>
                    </td>
                    <td style="height: 65px">{{ getCityLabel(data?.city) }}</td>
                    <td style="height: 65px">{{ data?.count }}</td>
                    <td style="height: 65px">{{ data?.adport_count }}</td>
                    <td style="height: 65px">{{ data?.score }}</td>
                  </tr>
                </tbody>
              </nz-table>
            </nz-card>
          </div>

          <!-- 13类线索报告排行 -->
          <div nz-col class="gutter-row" nzSpan="12" style="display: flex; align-items: stretch; margin-bottom: 24px;">
            <nz-card [nzTitle]="titleTemplate3" style="width: 100%;">
              <ng-template #titleTemplate3>
                <div class="title-container">
                  <span>13类线索报告排行</span>
                  <nz-select class="statistics_select" [(ngModel)]="XStiConsumeTime"
                    (ngModelChange)="XStiConsumeTimeChange()" nzDropdownMatchSelectWidth="false"
                    [nzDropdownStyle]="{ 'minWidth': '120px' }">
                    <nz-option *ngFor="let option of granularityOptions" [nzValue]="option.value"
                      [nzLabel]="option.text">
                    </nz-option>
                  </nz-select>
                </div>
              </ng-template>
              <nz-table #ajaxTable2 nzShowSizeChanger [nzFrontPagination]="false" [nzData]="XSListOfData"
                [nzLoading]="XSLoading" [nzFrontPagination]="false" [nzShowPagination]="false">
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>地区</th>
                    <th>报告数量</th>
                    <th>采编数量</th>
                    <th>分数</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of ajaxTable2.data; index as i">
                    <td>
                      <span class="ranking"
                        [ngClass]="{'first': ((pageIndex-1)*pageSize + (i+1)) == 1, 'second': ((pageIndex-1)*pageSize + (i+1)) == 2, 'third': ((pageIndex-1)*pageSize + (i+1)) == 3}">
                        {{ (pageIndex-1)*pageSize + (i+1) }}
                      </span>
                    </td>
                    <td style="height: 65px">{{ getCityLabel(data?.city) }}</td>
                    <td style="height: 65px">{{ data?.count }}</td>
                    <td style="height: 65px">{{ data?.adport_count }}</td>
                    <td style="height: 65px">{{ data?.score }}</td>
                  </tr>
                </tbody>
              </nz-table>
            </nz-card>
          </div>

          <!-- 会战战果排行 -->
          <div nz-col class="gutter-row" nzSpan="12" style="display: flex; align-items: stretch; margin-bottom: 24px;">
            <nz-card [nzTitle]="titleTemplate4" style="width: 100%;">
              <ng-template #titleTemplate4>
                <div class="title-container">
                  <span>会战战果排行</span>
                  <nz-select class="statistics_select" [(ngModel)]="HZtiConsumeTime"
                    (ngModelChange)="HZtiConsumeTimeChange()" nzDropdownMatchSelectWidth="false"
                    [nzDropdownStyle]="{ 'minWidth': '120px' }">
                    <nz-option *ngFor="let option of HZOptions" [nzValue]="option.id" [nzLabel]="option.task_name">
                    </nz-option>
                  </nz-select>
                </div>
              </ng-template>
              <nz-table #ajaxTable3 nzShowSizeChanger [nzFrontPagination]="false" [nzData]="HZListOfData"
                [nzLoading]="HZLoading" [nzFrontPagination]="false" [nzShowPagination]="false">
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>地区</th>
                    <th>报告数量</th>
                    <th>采编数量</th>
                    <th>分数</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let data of ajaxTable3.data; index as i">
                    <td>
                      <span class="ranking"
                        [ngClass]="{'first': ((pageIndex-1)*pageSize + (i+1)) == 1, 'second': ((pageIndex-1)*pageSize + (i+1)) == 2, 'third': ((pageIndex-1)*pageSize + (i+1)) == 3}">
                        {{ (pageIndex-1)*pageSize + (i+1) }}
                      </span>
                    </td>
                    <td style="height: 65px">{{ getCityLabel(data?.city) }}</td>
                    <td style="height: 65px">{{ data?.count }}</td>
                    <td style="height: 65px">{{ data?.adport_count }}</td>
                    <td style="height: 65px">{{ data?.score }}</td>
                  </tr>
                </tbody>
              </nz-table>
            </nz-card>
          </div>
        </div>
      </div>
    </div>
  </app-card-light>
</div>
