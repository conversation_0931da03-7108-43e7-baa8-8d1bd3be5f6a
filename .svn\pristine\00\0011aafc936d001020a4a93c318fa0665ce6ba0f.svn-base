import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService,NzModalService } from 'ng-zorro-antd';
import { SourceInfo } from '../intelligence-models/intelligence-model';
import { IntelligeceService } from '../../service/intelligece.service';

@Component({
  selector: 'app-intelligence-source-edit',
  template: '<app-intelligence-source-info [source_param]="source_param" [_finashLoading]="_finashLoading" (formSubmit)="editSource($event)"></app-intelligence-source-info>',
  styles: ['']
})
export class IntelligenceSourceEditComponent implements OnInit {

  source_param: any = {};
  _finashLoading: boolean = false;
  editSource(assetInfo: SourceInfo) {
    this._finashLoading = true;
    this.intel_s.edit_source(assetInfo).subscribe(
      (res) => {
        if (res) {
          this._finashLoading = false;
          if (res.status == 1) {
            this.nzMessage.success('编辑情报源成功!');
            this.router.navigate(['/intelligence/intelligence-source'], {
            });
            /* this.jump_web_server(); */
          } else {
            this.nzMessage.error(res.msg);
          }
          return;
        }
      },
      (error) => {
        this._finashLoading = false;
        this.nzMessage.error(error.message);
      }
    );
  }
  /* jump_web_server() {
    this.nzModal.confirm({
      nzTitle: '编辑情报源后需要重启Web服务器，是否需要跳转到该界面?',
      nzIconType:"info-circle",
      nzOkType:"info",
      nzMaskStyle:{backgroundColor: 'rgba(0, 0, 0, 0.19)'},
			nzOkText:"确定",
      nzOnOk: () => {
        return new Promise((resolve, reject) => {
          window.location.href = '/isop#/route/systemControl/web';
        })
      }
    });
  } */
  constructor(private nzMessage: NzMessageService, private intel_s: IntelligeceService, private router: Router,private nzModal: NzModalService,private activeRoute: ActivatedRoute,) { }

  ngOnInit() {
    this.source_param = this.activeRoute.snapshot.queryParams;
    //console.log(this.activeRoute.snapshot.queryParams);
  }

}
