import {
  NgModule,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
} from "@angular/core";
import { CommonModule, DatePipe } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { CoreModule } from "../core";
// import { DatePipe } from '@angular/common';

import { InvoleRoutingModule } from "./invole-routing.module";
import { InvoleReportComponent } from "./common/invole-report/invole-report.component";

// echarts
import { NgxEchartsModule } from "ngx-echarts";
// 情报库
import { InvoleIntelligenceComponent } from "./common/invole-intelligence/invole-intelligence.component";
// 情报统计
import { InvoleDashComponent } from "./common/invole-dash/invole-dash.component";
// import { ReportsComponent } from "./common/invole-report/reports/reports.component";

import { NgZorroAntdModule } from "ng-zorro-antd";
import { NewsBatchUploadComponent } from "./components/news-batch-upload/news-batch-upload.component";

import { NewsItemCardComponent } from "./components/news-item-card/news-item-card.component";
import { NewsAttachmentUploadComponent } from "./components/news-attachment-upload/news-attachment-upload.component";
import { NewsDocumentUploadComponent } from "./components/news-document-upload/news-document-upload.component";
import { NewsDescriptionComponent } from "./components/news-description/news-description.component";
import { RelatedSimpleTableComponent } from "./components/related-simple-table/related-simple-table.component";
// import { NewsJudgeTaskCreatedComponent } from "./components/news-judge-task-created/news-judge-task-created.component";
// import { NewsBasicInfoComponent } from "./components/news-basic-info/news-basic-info.component";
import { FormsModule } from "@angular/forms";
import { InvoleSubscribeComponent } from "./common/invole-subscribe/invole-subscribe.component";

import { DragDropModule } from "@angular/cdk/drag-drop";
import { NewsSingleEntryComponent } from "./components/news-single-entry/news-single-entry.component";
import { NewsDetailComponent } from "./components/news-detail/news-detail.component";
import { dateFilter } from "./news.pipe.pipe";
import { categoryColorFilter } from "./news.pipe.pipe";
import { relatedTypeFilter } from "./news.pipe.pipe";
import { taskStatusFilter } from "./news.pipe.pipe";
import { sourceTypeFilter } from "./news.pipe.pipe";
import { InvoleAiSourceComponent } from "./common/invole-ai-source/invole-ai-source.component";
import { InvolePromptTemplateComponent } from "./common/invole-prompt-template/invole-prompt-template.component";
import { UploadCorpusComponent } from "./components/upload-corpus/upload-corpus.component";

// import { CardLightComponent } from "app/core/components";

@NgModule({
  declarations: [
    InvoleReportComponent,
    InvoleIntelligenceComponent,
    InvoleDashComponent,
    NewsItemCardComponent,
    NewsDescriptionComponent,
    NewsDocumentUploadComponent,
    NewsBatchUploadComponent,
    UploadCorpusComponent,
    InvoleSubscribeComponent,
    NewsAttachmentUploadComponent,
    // NewsJudgeTaskCreatedComponent,
    NewsSingleEntryComponent,

    NewsDetailComponent,
    RelatedSimpleTableComponent,
    // NewsBasicInfoComponent,

    // 管道
    dateFilter,
    categoryColorFilter,
    relatedTypeFilter,
    taskStatusFilter,
    sourceTypeFilter,
    InvoleAiSourceComponent,
    InvolePromptTemplateComponent,
    UploadCorpusComponent,
  ],
  providers: [DatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
  entryComponents: [NewsSingleEntryComponent],
  imports: [
    CommonModule,
    CoreModule,
    InvoleRoutingModule,
    NgZorroAntdModule,
    FormsModule,
    ReactiveFormsModule,
    NgxEchartsModule,
    DragDropModule,
  ],
  // schemas:[CUSTOM_ELEMENTS_SCHEMA]
})
export class InvoleModule {}
