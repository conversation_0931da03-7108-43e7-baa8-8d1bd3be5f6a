/* 去掉outline样式 */
button
optgroup
select
textarea
input:focus
select:focus
textarea:focus
button:focus
a:focus
    outline: none
a
    color: #68b92e
    &:focus, &:hover
        // color: #377906
        text-decoration: none
    &:focus {
        color #0056bb
    }

// 表单的标签
.col-form-label
    color: #212121
.form-control
    padding: 5px 10px
    //font-size: 13px
    font-size: 14px !important
    height: 35px !important
    line-height: 20px
    border-radius: 3px
    border-color: #c2c2c2
    //color #212121 !important
    color: rgba(0, 0, 0, 0.65) !important
    &:focus
        color #D9D9D9
        border-color: #68b92e
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(104, 185, 46, .6)
.form-control::-webkit-input-placeholder 
    color #D9D9D9 !important

select.form-control:not([size]):not([multiple])
    height: 30px

.form-group
    margin-bottom: 0.5rem

// // 时间控件
// .flatpickr-time input
// .flatpickr-day
// .flatpickr-month
//     color: #212121
//     fill: #212121
// span.flatpickr-weekday
//     color: #858585;

.region-item
    display: inline-block
    padding: 5px 0px 5px 10px
    min-width: 80px
    a
        color: #212121

.custom-control
    display :inline-flex
    cursor :pointer
.custom-control-input
    background-color: white
    border: 1px solid #c2c2c2
.custom-control-input:focus~.custom-control-label::before
    box-shadow: none
// checkbox选中背景色
.custom-control-input:checked~.custom-control-label::before
    background-color: #6fba2c
    border: none
.custom-control-input[disabled]:checked~.custom-control-label::before
    background-color: #eee

.flex_display
    display flex !important
.flex_1
    flex 1
.font_family_label
    font-size: 14px;
    //font-family: PingFangSC, PingFangSC-Regular !important;
    font-weight: 400 !important;
    color: #000000 !important;
    opacity: 0.85 !important;
    line-height: 22px !important;
    width:94px;
.ns_nowrap
    white-space: nowrap;

.ns_collopse
    color:#52afff !important;


//情报统计下的日期选择样式
.statistics_select
    div
        border-style: none;
    .ant-select-focused .ant-select-selection, .ant-select-selection:focus, .ant-select-selection:active, .ant-input:focus
        border-color: white;
        box-shadow: 0 0 0 2px white;