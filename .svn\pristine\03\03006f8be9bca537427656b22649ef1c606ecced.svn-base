import { Pipe, PipeTransform } from "@angular/core";
import moment from "moment";

@Pipe({
    name: "relatedTypeFilter",
})
export class relatedTypeFilter implements PipeTransform {
    private titleMap = {
        ipv4: " ipv4",
        ipv6: "ipv6",
        url: "URL",
        domain: "域名",
        file: "样本hash",
        vulnerability: "漏洞",
        threat_actor: "攻击组织",
    };
    transform(value: any, args?: any): any {
        return this.titleMap[value] || value;
    }
}
@Pipe({
    name: "categoryColorFilter",
})
export class categoryColorFilter implements PipeTransform {
    private CATEGORY_COLOR = {
        // 8
        参考情报: "#CC6633",
        舆论情报: "#f1a45d",
        政策情报: "#fc8452",
        动态情报: "#ee6666",
        态势情报: "#0072ee",
        预警情报: "#2db7f5",
        主体情报: "#9a60b4",
        其他情报: "#666666",
        // 13
        木马攻击: '#eacd76',
        钓鱼网站: '#3D9970',
        钓鱼邮件: '#01FF70',
        水坑攻击: '#39CCCC',
        爆破扫描: '#FF4136',
        僵尸网络传播: '#808080',
        蠕虫病毒传播: '#665757',
        网页篡改: '#f3704b',
        数据泄露: '#0074D9',
        特定漏洞: '#FF851B',
        通用漏洞: '#FFDC00',
        黑客团伙: '#B10DC9',
        其他类线索: '#CCCCCC',
    };
    transform(value: any, args?: any): any {

        return this.CATEGORY_COLOR[value] || value;
    }
}
@Pipe({
    name: "taskStatusFilter",
})
export class taskStatusFilter implements PipeTransform {
    private STATUS_MAP = {
        Activity_chubuyanpan: "初步研判",
        Activity_fenpeixietongyanpan: "指派协同研判",
        Activity_duorenyanpan: "协同研判",
        Activity_jieguoguidang: "结果归档",
        Activity_shenpi: "审批",
        Activity_bianjibaogao: "编辑报告",
        Activity_bianji: "编辑",
        end: "任务结束",
    };

    transform(value: any, args?: any): any {
        return this.STATUS_MAP[value] || value;
    }
}

@Pipe({
    name: "dateFilter",
})
export class dateFilter implements PipeTransform {
    transform(value: any, args?: any): any {
        if (!value) return "";
        let result = String(value).includes("Z")
            ? moment(value).utcOffset(0).format("YYYY-MM-DD HH:mm:ss")
            : moment(value).format("YYYY-MM-DD HH:mm:ss"); //后端存储时间时区有问题
        return result == "Invalid date" ? value : result;
    }
}

@Pipe({
    name: "sourceTypeFilter",
})
export class sourceTypeFilter implements PipeTransform {
    private sourceMap = {
        0: "ChatGPT",
        1: "Kimi",
        2: "Ernie",
    };
    transform(value: any, args?: any): any {
        return this.sourceMap[value] || value;
    }
}
