<div class="asset_library">
    <div class="asset_library__header">
        <div style="display: flex; align-items: center; justify-content: space-between; width: 73%">
            <div class="search">
                <span style="width: 142px">关键字搜索：</span>
                <input type="text" [(ngModel)]="params.keywords" nz-input placeholder="任意关键字检索" />
            </div>
            <div class="timer" style="width: calc(100vw * (460 / 1920))">
                <span style="width: 142px">更新时间筛选：</span>
                <nz-range-picker [(ngModel)]="dateRange" [nzRanges]="ranges" (ngModelChange)="onDateRangeChange($event)"></nz-range-picker>
            </div>
            <div class="btn">
                <button nz-button nzType="primary" (click)="search()">搜索</button>
            </div>
        </div>
    </div>
    <div class="asset_library__content" style="background: #fff">
        <div class="asset_library__content__tab">
            <nz-tabset (nzSelectChange)="onTabChange($event)">
                <nz-tab nzTitle="APP">
                    <div *ngFor="let item of listOfData; let i = index">
                        <div class="apk-info-container">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div style="padding: 20px">
                                <div class="apk-info" (click)="expand(item)">
                                    <div class="title">
                                        <span class="name"> {{ item.name }}</span>
                                        <div class="tag">{{ item.type }}</div>
                                    </div>
                                    <div class="company">
                                        {{ item.company }}
                                        <i *ngIf="item.isExpand" nz-icon nzType="down" nzTheme="outline"></i>
                                        <i *ngIf="!item.isExpand" nz-icon nzType="up" nzTheme="outline"></i>
                                    </div>
                                </div>

                                <div class="time-info">
                                    <div>
                                        <span class="time-title"> 发布时间: </span>
                                        <span class="time-content">{{ item.created }}</span>
                                    </div>
                                    <div>
                                        <span class="time-title"> 更新时间: </span>
                                        <span class="time-content">{{ item.modified }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="APK-details" *ngIf="item.isExpand">
                            <div>
                                <div class="apk-company">
                                    <p class="server" style="margin-bottom: 16px">
                                        <span class="tale">所属公司:</span><span class="name" style="margin-right: 10px">{{ item.company }}</span>
                                    </p>
                                </div>

                                <p class="server" style="margin-bottom: 16px">
                                    <span class="tale">appid:</span>
                                    <span class="name" style="margin-right: 10px">{{ item.appid }}</span>
                                </p>
                                <p class="server" style="margin-bottom: 16px">
                                    <span class="tale">ICP 备案号:</span><span class="name" style="margin-right: 10px">{{ item.icp }}</span>
                                </p>

                                <div>
                                    <p class="server">
                                        <span class="tale">app_url: </span>
                                        <a class="name" target="_blank">{{ item.url }} </a>
                                    </p>
                                    <p class="server">
                                        <span class="tale">应用简介: </span>
                                        <span class="name">
                                            {{ item.detail }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </nz-tab>
                <nz-tab nzTitle="小程序">
                    <div *ngFor="let item of listOfData">
                        <div class="apk-info-container">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div style="padding: 20px">
                                <div class="apk-info" (click)="expand(item)">
                                    <div class="title">
                                        <span class="name"> {{ item.name }}</span>
                                        <div class="tag">{{ item.type }}</div>
                                    </div>
                                    <div class="company">
                                        {{ item.company }}
                                        <i *ngIf="item.isExpand" nz-icon nzType="down" nzTheme="outline"></i>
                                        <i *ngIf="!item.isExpand" nz-icon nzType="up" nzTheme="outline"></i>
                                    </div>
                                </div>

                                <div class="time-info">
                                    <div>
                                        <span class="time-title"> 发布时间: </span>
                                        <span class="time-content">{{ item.created }}</span>
                                    </div>
                                    <div>
                                        <span class="time-title"> 更新时间: </span>
                                        <span class="time-content">{{ item.modified }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="APK-details" *ngIf="item.isExpand">
                            <div>
                                <div class="apk-company">
                                    <p class="server" style="margin-bottom: 16px">
                                        <span class="tale">所属公司:</span><span class="name" style="margin-right: 10px">{{ item.company }}</span>
                                    </p>
                                </div>
                                <p class="server" style="margin-bottom: 16px">
                                    <span class="tale">appid:</span><span class="name" style="margin-right: 10px">{{ item.appid }}</span>
                                </p>
                                <p class="server" style="margin-bottom: 16px">
                                    <span class="tale">小程序原始 id:</span><span class="name" style="margin-right: 10px">{{ item.original_id }}</span>
                                </p>
                                <p class="server" style="margin-bottom: 16px">
                                    <span class="tale">ICP 备案号:</span><span class="name" style="margin-right: 10px">{{ item.icp }}</span>
                                </p>
                                <p class="server" style="margin-bottom: 16px">
                                    <span class="tale">首页路径:</span><span class="name" style="margin-right: 10px">{{ item.home_path }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </nz-tab>
                <nz-tab nzTitle="公众号">
                    <div *ngFor="let item of listOfData">
                        <div class="apk-info-container">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div style="padding: 20px">
                                <div class="apk-info" (click)="expand(item)">
                                    <div class="title">
                                        <span class="name"> {{ item.name }}</span>
                                        <div class="tag">{{ item.type }}</div>
                                    </div>
                                    <div class="company">
                                        {{ item.company }}
                                        <i *ngIf="item.isExpand" nz-icon nzType="down" nzTheme="outline"></i>
                                        <i *ngIf="!item.isExpand" nz-icon nzType="up" nzTheme="outline"></i>
                                    </div>
                                </div>

                                <div class="time-info">
                                    <div>
                                        <span class="time-title"> 发布时间: </span>
                                        <span class="time-content">{{ item.created }}</span>
                                    </div>
                                    <div>
                                        <span class="time-title"> 更新时间: </span>
                                        <span class="time-content">{{ item.modified }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="APK-details" *ngIf="item.isExpand" style="height: 100%; overflow: hidden">
                            <div>
                                <div class="apk-company">
                                    <p class="server" style="margin-bottom: 16px">
                                        <span class="tale">所属公司:</span><span class="name" style="margin-right: 10px">{{ item.company }}</span>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <p class="server">
                                    <span class="tale">应用简介: </span>
                                    <span class="name">{{ item.detail }}</span>
                                </p>
                            </div>
                            <p class="server" style="margin-bottom: 16px">
                                <span class="tale">账号类型:</span><span class="name" style="margin-right: 10px">{{ item.wechat_type }}</span>
                            </p>
                            <p class="server" style="margin-bottom: 16px">
                                <span class="tale">公众号 fakeid:</span><span class="name" style="margin-right: 10px">{{ item.fakeid_id }}</span>
                            </p>
                        </div>
                    </div>
                </nz-tab>
            </nz-tabset>
            <nz-empty style="margin: 136px 8px" *ngIf="listOfData.length <= 0"></nz-empty>
        </div>
        <div class="paginator" *ngIf="listOfData.length > 0">
            <span style="margin-right: 8px"> 共 {{ total }} 条 </span>
            <nz-pagination
                [(nzPageIndex)]="params.page_num"
                [(nzPageSize)]="params.page_size"
                (nzPageIndexChange)="onPageIndexChange($event)"
                (nzPageSizeChange)="onPageSizeChange($event)"
                [nzFrontPagination]="false"
                [nzBordered]="false"
                [nzPageSizeOptions]="[10, 20, 30, 50]"
                [nzTotal]="total"
                nzShowSizeChanger
                nzShowQuickJumper
                [nzLoading]="isLoading"
            ></nz-pagination>
        </div>
    </div>
</div>
