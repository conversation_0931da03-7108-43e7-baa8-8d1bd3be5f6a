import { Component, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { NzModalService, NzDrawerService } from "ng-zorro-antd";
import { InvoleDatabaseService } from "../../services/invole-database.service";

import { IntelligeceService } from "app/intelligence/service/intelligece.service";
import { Router } from "@angular/router";
import { DataService } from "app/invole/tools/dataServices";
import { intel_Service } from "app/invole/tools/intelService";

import { Subscription } from "rxjs";

import { RoutingHistoryService } from "app/invole/tools/RoutingHistoryService";

// import { NewsCoreService } from "../../services/news-core.service";
import { NewsAttachmentUploadComponent } from "app/invole/components/news-attachment-upload/news-attachment-upload.component";
// import { NewsJudgeTaskCreatedComponent } from "app/invole/components/news-judge-task-created/news-judge-task-created.component";
// import { NewsAttachmentUploadComponent } from "../../components/news-attachment-upload/news-attachment-upload.component";
// import { NewsJudgeTaskCreatedComponent } from "../../components/news-judge-task-created/news-judge-task-created.component";

@Component({
    selector: "app-news-detail",
    templateUrl: "./news-detail.component.html",
    styleUrls: ["./news-detail.component.styl"],
})
export class NewsDetailComponent implements OnInit {
    topic_base_info: any = {};
    related: any = {};
    loading: boolean = true;
    tabsOrder = [
        "file",
        "vulnerability",
        "ipv4",
        "ipv6",
        "domain",
        "url",
        "threat_actor",
        // "email",
        // "attachment",
    ];
    tabs = [];
    // tabs = ["漏洞", "域名", "URL"];

    //   simpleTypesList = ['ipv4', 'domain', 'url', 'email', 'attachment']
    fileTableData: any[] = [];

    countEffectiveValues(obj) {
        let count = 0;
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const value = obj[key];
                if (value !== null && value !== undefined) {
                    if (Array.isArray(value)) {
                        // 如果值是数组，并且数组不为空，则增加数组的长度
                        if (value.length > 0) {
                            count += value.length;
                        }
                    } else {
                        // 如果值不是数组，则简单地增加1
                        count += 1;
                    }
                }
            }
        }
        return count;
    }

    // getFileRows(fileObj: any): any[] {
    //   console.log(fileObj, "fileObj");

    //   return Object.keys(fileObj).map((key) => ({
    //     key: key,
    //     value: Array.isArray(fileObj[key])
    //       ? fileObj[key].join(", ")
    //       : fileObj[key],
    //   }));
    // }

    params = {
        _id: "",
    };
    countStyle = {
        background: "rgba(85, 167, 34, 0.1)",
        color: "#68b92e",
        marginLeft: "10px",
    };
    constructor(
        private route: ActivatedRoute,
        private service: InvoleDatabaseService,
        private modalService: NzModalService,
        private drawerService: NzDrawerService,
        private intel_s: IntelligeceService,
        private dataService: DataService,
        private intel_Service: intel_Service,
        private router: Router,
        private routingHistoryService: RoutingHistoryService
    ) { }
    previousRoute: string | null = null;

    ngOnInit() {


        this.route.queryParams.subscribe((queryParams) => {
            this.params._id = queryParams["id"];
            // console.log(queryParams["id"], 'queryParams["id"]');

            this.getDetail(this.params);

        });
        this.previousRoute = this.routingHistoryService.getPreviousRoute();

    }

    isObjectNotEmpty(object: any): boolean {
        return object && Object.keys(object).length > 0;
    }

    isObjectEmpty() {
        if (
            Object.values(this.related.file).every(
                (value) => value === null || value === undefined
            ) &&
            Object.values(this.related.vulnerability).every(
                (value) => value === null || value === undefined
            )
        ) {
            this.tabsOrder = this.tabsOrder.filter(
                (tab) => tab !== "file" && tab !== "vulnerability"
            );
        }
    }

    hashArrayWithKeyValue: { key: string; value: any }[];
    vulnerabilityArray: { key: string; value: any }[];

    formatData(data) {
        let result = '';
        // for (const key in data) {
        //     if (data.hasOwnProperty(key)) {
        //         for (const value of data[key]) {
        //             result += `"${key}":[${value}],\n`;
        //         }
        //     }
        // }
        data.forEach(item => {
            if (item.value !== null && item.value !== undefined && item.value !== "") {
                result += `"${item.key}":"${item.value}",\n`;
            }
        });
        return result.trim(); // 去除末尾的逗号和换行符
    }

    getDetail(params) {
        this.loading = true;
        this.service.getTopicDetail(params).subscribe(
            (resp) => {
                this.loading = false;
                if (resp.code === 200) {
                    if (resp.data.custom_labels != null) {
                        resp.data.custom_labels = resp.data.custom_labels.filter(label => label !== "");
                    }
                    this.topic_base_info = resp.data;
                    this.related = resp.data.ioc;
                    // console.log(this.related.file, "this.related");

                    const data = Object.keys(this.related.file)
                        .map((key) => ({
                            key: key,
                            value: this.related.file[key],
                        }))
                        .filter(
                            (item) =>
                                item.value !== null &&
                                item.value !== undefined &&
                                item.value !== ""
                        );



                    this.hashArrayWithKeyValue = data.reduce((acc, item) => {
                        item.value.forEach(value => {
                            acc.push({
                                key: item.key,
                                value: [value]
                            });
                        });
                        return acc;
                    }, []);




                    const vuln = Object.keys(this.related.vulnerability)
                        .map((key) => ({
                            key: key,
                            value: this.related.vulnerability[key],
                        }))
                        .filter(
                            (item) =>
                                item.value !== null &&
                                item.value !== undefined &&
                                item.value !== ""
                        );

                    this.vulnerabilityArray = vuln.reduce((acc, item) => {
                        item.value.forEach(value => {
                            acc.push({
                                key: item.key,
                                value: [value]
                            });
                        });
                        return acc;
                    }, []);

                    this.tabs = [];
                    this.isObjectEmpty();

                    let addedTabs = new Set<string>();

                    this.tabsOrder.forEach((v) => {
                        // console.log(v, "vvvv");

                        const relatedItem = this.related[v];

                        for (let key in relatedItem) {
                            if (this.isObjectNotEmpty(relatedItem[key])) {
                                if (!addedTabs.has(v)) {
                                    this.tabs.push(v);
                                    addedTabs.add(v);
                                }
                            }
                        }
                    });
                }
            },
            (error) => {
                this.loading = false;
            }
        );
    }



    countNonEmptyProperties(obj: any): number {
        let count = 0;

        if (typeof obj === 'object' && obj !== null) {
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    if (Array.isArray(obj[key])) {
                        // 如果是数组，递归计算数组中非空元素的数量
                        count += this.countNonEmptyArrayElements(obj[key]);
                    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                        // 如果是嵌套对象，递归计算对象中非空属性的数量
                        count += this.countNonEmptyProperties(obj[key]);
                    } else if (obj[key] !== null && obj[key] !== undefined) {
                        // 如果是基本类型且非空，计数加一
                        count++;
                    }
                }
            }
        }

        return count;
    }

    countNonEmptyArrayElements(arr: any[]): number {
        let count = 0;
        for (const element of arr) {
            if (element !== null && element !== undefined) {
                // 对于数组中的元素，如果它是对象，则递归计算；否则，直接计数
                if (typeof element === 'object' && element !== null) {
                    count += this.countNonEmptyProperties(element);
                } else {
                    count++;
                }
            }
        }
        return count;
    }

    getRelatedLength(item: string): number {

        return this.countNonEmptyProperties(this.related[item]);
    }

    // showUploadAttachment(item) {
    //   const modalService = this.modalService.create({
    //     nzTitle: "上传附件",
    //     nzWidth: 600,
    //     nzFooter: null,
    //     nzMaskClosable: false,
    //     nzContent: NewsAttachmentUploadComponent,
    //     nzComponentParams: {
    //       item: item,
    //     },
    //   });
    //   modalService.afterClose.subscribe((result) => {
    //     result === true && this.getDetail(this.params);
    //   });
    // }
    // showJudgeDrawer(item) {
    //   this.drawerService.create({
    //     nzTitle: "发起资讯研判",
    //     nzWidth: 640,
    //     nzMaskClosable: false,
    //     nzContent: NewsJudgeTaskCreatedComponent,
    //     nzContentParams: {
    //       newsItem: item,
    //     },
    //   });
    // }
    back() {
        let data = JSON.parse(localStorage.getItem("detailMessage"));
        let list = JSON.parse(localStorage.getItem("intelligenceMessage"));



        if (this.previousRoute === "/invole/invole-intelligence") {
            const dataToSend = list;
            this.intel_Service.setData(dataToSend);
            this.router.navigate(["/invole/invole-intelligence"]);
        } else {
            const dataToSend = data;
            this.dataService.setData(dataToSend);
            this.router.navigate(["/invole/invole-subscribe"]);
        }
    }
}
