<div class="container" style="height: 100%">
    <div class="header">
        <h1>信息监测</h1>
        <button nz-button nzType="primary" style="padding: 5px" (click)="openMonitoring()">新建</button>
    </div>
    <!-- 新建监测条目 -->
    <nz-drawer
        [nzBodyStyle]="{ height: 'calc(100% - 55px)', overflow: 'auto', 'padding-bottom': '53px' }"
        [nzMaskClosable]="false"
        [nzWidth]="350"
        [nzVisible]="visible"
        nzTitle="新建检测条目"
        (nzOnClose)="closeMonitoring()"
    >
        <form nz-form [formGroup]="validateForm" (ngSubmit)="submitForm($event, validateForm.value)">
            <div nz-row>
                <div nz-col>
                    <nz-form-item>
                        <nz-form-label nzRequired>监测标题</nz-form-label>
                        <nz-form-control style="flex: 1">
                            <input formControlName="monitor_title" style="padding: 5px" nz-input placeholder="请输入监测标题" />
                        </nz-form-control>
                    </nz-form-item>
                </div>
            </div>

            <div nz-row>
                <div nz-col>
                    <nz-form-item>
                        <nz-form-label nzRequired>监测词</nz-form-label>
                        <nz-form-control style="flex: 1">
                            <textarea
                                formControlName="monitor_keywords"
                                nz-input
                                rows="2"
                                placeholder="请输入监测词"
                                style="padding: 5px; width: 100%; border: 1px solid #d5cece; height: 100px"
                            ></textarea>
                        </nz-form-control>
                    </nz-form-item>
                </div>
            </div>
            <div nz-row>
                <div nz-col style="text-align: right">
                    <nz-form-item>
                        <nz-form-control>
                            <button nz-button nzType="primary" class="ant-btn" [disabled]="!validateForm.valid">提交</button>
                            <button nz-button class="ant-btn" style="margin-left: 8px" (click)="resetForm($event)">重置</button>
                        </nz-form-control>
                    </nz-form-item>
                </div>
            </div>
        </form>
        <!-- <div class="footer">
            <button type="button" class="ant-btn" style="margin-right: 8px"><span>Cancel</span></button>
            <button type="button" class="ant-btn ant-btn-primary"><span>Submit</span></button>
        </div> -->
    </nz-drawer>

    <div class="search-section">
        <div class="search-group" *ngFor="let item of monitorList; let i = index" (click)="expandMonitoring(i)" [ngClass]="currentIndex === i ? 'box-container' : ''">
            <span style="max-width: 58px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block">{{ item.monitor_title }}</span>

            <div style="cursor: pointer">
                <i nz-icon nzType="edit" (click)="editMonitorData(item)" nzTheme="outline"></i>
                <i nz-popconfirm nzTitle="确定删除吗？" (nzOnConfirm)="deleteMonitorData(item)" style="margin-left: 8px" nz-icon nzType="delete" nzTheme="outline"></i>
            </div>
        </div>
    </div>

    <div class="message-list">
        <!-- 使用 displayMessages 替代 messages -->
        <div class="message-item" *ngFor="let message of displayMessages">
            <!-- 原有内容结构保持不变 -->
            <div class="message-header">
                <span class="message-group">{{ message.group }}</span>
                <span class="message-sender">{{ message.sender }}</span>
                <span class="message-time">{{ message.date }} {{ message.time }}</span>
            </div>
            <div class="message-content">
                <div class="content-line" *ngFor="let content of message.contents">
                    <div class="content-title">{{ content }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分页器 -->
    <div class="pagination">
        <nz-pagination
            [nzPageIndex]="currentPage"
            [nzTotal]="total"
            [nzPageSize]="pageSize"
            nzShowSizeChanger
            (nzPageIndexChange)="onPageChange($event)"
            (nzPageSizeChange)="onPageSizeChange($event)"
        ></nz-pagination>
    </div>
</div>
