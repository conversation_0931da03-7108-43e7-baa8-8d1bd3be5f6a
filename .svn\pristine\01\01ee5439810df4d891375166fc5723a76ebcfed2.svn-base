<div class="dashboard-box-new">
    <div class="lost-host">
        <div class="head-select">
            <span>时间范围：</span>
            <nz-select
                style="width: 325px"
                [(ngModel)]="dataTableConfig.searchField"
                nzPlaceHolder="选择筛选字段"
                (ngModelChange)="changeSearchField($event)"
            >
                <nz-option
                    *ngFor="let item of searchOptionList"
                    nzValue="{{ item.value }}"
                    nzLabel="{{ item.name }}"
                ></nz-option>
            </nz-select>
            <div
                style="width: 325px"
                *ngIf="dataTableConfig.searchField === '0'"
            >
                <nz-range-picker
                    [nzFormat]="'yyyy-MM-dd HH:mm:ss'"
                    [(ngModel)]="dateRange"
                    (ngModelChange)="onChange($event)"
                    (nzOnOk)="onOk($event)"
                    nzShowTime
                ></nz-range-picker>
            </div>
            <button nz-button nzType="default" (click)="showMonitorDrawer()">
                配置
            </button>
            <button nz-button nzType="primary" (click)="exportData()">
                导出
            </button>
        </div>
        <div nz-col nzSpan="24" style="padding: 10px">
            <div class="content-card">
                <div class="total-card">
                    <div class="title">失陷主机数</div>
                    <div class="num">{{ countNum.lostDipsCount }}</div>
                </div>
                <div class="total-card">
                    <div class="title">新增失陷主机数</div>
                    <div class="num">{{ countNum.newAddLostDdips }}</div>
                </div>
                <div class="total-card">
                    <div class="title">攻击事件数</div>
                    <div class="num">{{ countNum.killEventCount }}</div>
                </div>
                <div class="total-card">
                    <div class="title">攻击源主机数</div>
                    <div class="num">{{ countNum.sipsCount }}</div>
                </div>
            </div>
        </div>
        <div nz-col nzSpan="24" style="padding: 10px">
            <nz-card
                class="dash-card-new"
                [nzLoading]="loading.dipsChangeCount"
                [nzTitle]="titleTemplate"
                [nzBodyStyle]="{ height: '450px' }"
            >
                <div
                    echarts
                    [options]="dipsChangeCountChartOption"
                    class="dash-chart-new"
                ></div>
            </nz-card>
        </div>
        <div nz-col nzSpan="12" style="padding: 10px">
            <nz-card
                class="dash-card-new"
                [nzLoading]="loading.atkGroupByCount"
                [nzTitle]="title1Template"
                [nzBodyStyle]="{ height: '450px' }"
            >
                <div
                    echarts
                    [options]="getAtkGroupByCountChartOption"
                    class="dash-chart-new"
                ></div>
            </nz-card>
        </div>
        <div nz-col nzSpan="12" style="padding: 10px">
            <nz-card
                class="dash-card-new"
                [nzLoading]="loading.dipsGroupByCity"
                [nzTitle]="title2Template"
                [nzBodyStyle]="{ height: '450px' }"
            >
                <div
                    echarts
                    [options]="getDipsGroupByCityChartOption"
                    class="dash-chart-new"
                ></div>
            </nz-card>
        </div>
        <div nz-col nzSpan="12" style="padding: 10px">
            <div class="table" style="margin-bottom: 0">
                <nz-card
                    [nzLoading]="loading.table"
                    [nzTitle]="title3Template"
                    [nzBodyStyle]="{ height: '510px', padding: '10px' }"
                >
                    <nz-table
                        style="width: 100%; margin: auto"
                        nzSize="middle"
                        [nzBordered]="true"
                        [nzShowPagination]="false"
                        #typeTrendTableTop
                        [nzData]="remoteDomainTop10Data"
                        [nzLoading]="intelligenceQuantityLoading"
                    >
                        <thead>
                            <tr>
                                <th>远控域名</th>
                                <th>失陷主机数</th>
                                <th>事件数</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let data of typeTrendTableTop.data">
                                <td>{{ data.src_host }}</td>
                                <td>{{ data.count_dips }}</td>
                                <td>{{ data.count_kill_event }}</td>
                            </tr>
                        </tbody>
                    </nz-table>
                </nz-card>
            </div>
        </div>
        <div nz-col nzSpan="12" style="padding: 10px">
            <div class="table" style="margin-bottom: 0">
                <nz-card
                    [nzLoading]="loading.table1"
                    [nzTitle]="title4Template"
                    [nzBodyStyle]="{ height: '510px', padding: '10px' }"
                >
                    <nz-table
                        style="width: 100%; margin: auto"
                        nzSize="middle"
                        [nzBordered]="true"
                        [nzShowPagination]="false"
                        #typeTrendTable
                        [nzData]="victimHostTop10Data"
                        [nzLoading]="intelligenceQuantityLoading"
                    >
                        <thead>
                            <tr>
                                <th>失陷主机</th>
                                <th>地理信息</th>
                                <th>事件数</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let data of typeTrendTable.data">
                                <td>{{ data.dips }}</td>
                                <td>{{ data.dst_city }}</td>
                                <td>{{ data.count_kill_event }}</td>
                            </tr>
                        </tbody>
                    </nz-table>
                </nz-card>
            </div>
        </div>
    </div>
</div>
<ng-template #titleTemplate>
    <span class="lost-host-title">主机数量变化趋势</span>
</ng-template>
<ng-template #title1Template>
    <span class="lost-host-title">攻击恶意类型占比</span>
</ng-template>
<ng-template #title2Template>
    <span class="lost-host-title">失陷主机地区分布统计</span>
</ng-template>
<ng-template #title3Template>
    <span class="lost-host-title">远控域名排行TOP10</span>
</ng-template>
<ng-template #title4Template>
    <span class="lost-host-title">失陷主机排行TOP10</span>
</ng-template>
