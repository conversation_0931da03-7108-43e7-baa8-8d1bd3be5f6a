import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { HttpService } from 'app/core';
import { IndustryMappingServiceService } from 'app/core/service/industry-mapping-service.service';
import { $, namespace_produce as namespace } from 'app/shared';
import { EChartsOption } from "echarts/lib/echarts";

import 'echarts/lib/chart/line';
import 'echarts/lib/chart/bar';
import 'echarts/lib/chart/pie';
import 'echarts/lib/chart/radar';
// component examples:
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/title';
import 'echarts/lib/component/legend';
import { ReportService } from "./services/report.service";
import { locationService } from "./services/location";
import { getPieOption, getBarThreeOption, getTwoLineOption, getTwoPieOption, getTopLeftLineOption, getBottomRightLineOption, getBottomLeftLineOption, getTopRightLineOption } from "./common/reports";
import { NzDrawerService, NzMessageService } from "ng-zorro-antd";
import { ShieldVisualSubscribeDrawerComponent } from 'app/shield-visual/common/shield-visual-lost-host/common/shield-visual-subscribe-drawer/shield-visual-subscribe-drawer.component';
@Component({
    selector: 'app-shield-visual-lost-host',
    templateUrl: './shield-visual-lost-host.component.html',
    styleUrls: ['./shield-visual-lost-host.component.styl']
})
export class ShieldVisualLostHostComponent implements OnInit {
    // 以下数据已定义
    typeTrendData = [];
    intelligenceQuantityLoading: boolean = false;

    loading = {
        summary: false,
        process: false,
        source: false,
        category: false,
        keywords: false,
        wordCloud: false,
        topTopics: false,
        recommendTopics: false,
    };
    leftTopChartOption: EChartsOption;
    leftBottomChartOption: EChartsOption;
    rightTopChartOption: EChartsOption;
    rightBottomChartOption: EChartsOption;
    //中间部分
    centerTopLeftChartOption: EChartsOption;
    centerTopRightChartOption: EChartsOption;
    centerBottomLeftChartOption: EChartsOption;
    centerBottomRightChartOption: EChartsOption;

    cityDate = []
    tiConsumeTime = '30';

    optionList = [{ label: '最近一小时', value: '最近一小时', age: 20 }, { label: '最近一天', value: '最近一天', age: 22 }, { label: '最近一周', value: '最近一周', age: 24 }];
    selectedValue = { label: '最近一天', value: '最近一天', age: 22 };
    dateRange = []; // 保存选择的日期范围

    // 以上数据已定义
    constructor(
        private http: HttpService,
        private router: Router,
        private industryMapping: IndustryMappingServiceService,
        private location: locationService,
        private message: NzMessageService,
        private service: ReportService,
        private cdr: ChangeDetectorRef,
        private drawerService: NzDrawerService,
    ) {
        $(".container-box").removeClass("containerCustom");
    }
    ngOnDestroy() {
        $(".container-box").addClass("containerCustom");
        // this.cdr.detectChanges();
    }

    //日期变化函数
    onChange(result: Date[]): void {
        const convertToUnixTimestamp = (date: Date): number => {
            return Math.floor(date.getTime() / 1000); // 将日期转换为秒级 UNIX 时间戳
        };

        const formattedDates = result.map(date => convertToUnixTimestamp(new Date(date))); // 转换日期范围
        console.log(formattedDates); // 打印时间戳数组

        // this.searchData.time_range = formattedDates.join(','); // 将时间戳数组拼接成字符串
    }

    compareFn = (o1: any, o2: any) => (o1 && o2 ? o1.value === o2.value : o1 === o2);

    log(value: { label: string; value: string; age: number }): void {
        console.log(value);
    }

    showMonitorDrawer(data) {
        this.drawerService.create({
            nzTitle: '检索条件',
            nzContent: ShieldVisualSubscribeDrawerComponent,
            nzWidth: '540px',
            nzMaskClosable: false,
            nzContentParams: {
                isAdd: !data,
                id: data ? data.id : ''
            },
        }).afterClose.subscribe(resp => {
            if (resp) {
                // this.getTableData(this.filterCondition);
            }
        });
    }

    //处理查询城市列表 listOfData.threat_type
    getCityLabel(id): string {
        const found = this.cityDate.find(item => item.id == id);
        return found ? found.name : '未知';
    }
    //1、失陷主机行业统计
    getLeftTopChartOption() {
        let params = {
            frequence: this.tiConsumeTime
        };
        this.service.get_department_statistics(params).subscribe(
            (res) => {
                // console.log(res);

                if (res.status == 1) {

                    // 生成图表配置
                    this.leftTopChartOption = getBarThreeOption();
                } else {
                    this.message.error(res.msg);
                }
            }
        );
    }
    //3、组织影响主机数统计
    getRightBottomChartOption() {
        let params = {
            frequence: this.tiConsumeTime
        };
        this.service.get_department_statistics(params).subscribe(
            (res) => {
                // console.log(res);

                if (res.status == 1) {
                    // 生成图表配置
                    this.rightBottomChartOption = getTwoLineOption();

                } else {
                    this.message.error(res.msg);
                }
            }
        );
    }
    //1、中间左上ok
    getCenterTopLeftChartOption() {
        let params = {
            frequence: this.tiConsumeTime
        };
        this.service.get_department_statistics(params).subscribe(
            (res) => {
                // console.log(res);

                if (res.status == 1) {
                    // 生成图表配置
                    this.centerTopLeftChartOption = getTopLeftLineOption();

                } else {
                    this.message.error(res.msg);
                }
            }
        );
    }
    //2、中间右上ok
    getCenterTopRightChartOption() {
        let params = {
            frequence: this.tiConsumeTime
        };
        this.service.get_department_statistics(params).subscribe(
            (res) => {
                // console.log(res);

                if (res.status == 1) {
                    // 生成图表配置
                    this.centerTopRightChartOption = getTopRightLineOption();

                } else {
                    this.message.error(res.msg);
                }
            }
        );
    }
    //3、中间左下ok
    getCenterBottomLeftChartOption() {
        let params = {
            frequence: this.tiConsumeTime
        };
        this.service.get_department_statistics(params).subscribe(
            (res) => {
                // console.log(res);

                if (res.status == 1) {
                    // 生成图表配置
                    this.centerBottomLeftChartOption = getBottomLeftLineOption();

                } else {
                    this.message.error(res.msg);
                }
            }
        );
    }
    //4、中间右下ok
    getCenterBottomRightChartOption() {
        let params = {
            frequence: this.tiConsumeTime
        };
        this.service.get_department_statistics(params).subscribe(
            (res) => {
                // console.log(res);

                if (res.status == 1) {
                    // 生成图表配置
                    this.centerBottomRightChartOption = getBottomRightLineOption();

                } else {
                    this.message.error(res.msg);
                }
            }
        );
    }
    //2、失陷主机地区分布
    getLeftBottomChartOption() {
        let params = {
            frequence: this.tiConsumeTime
        }
        this.service.statisticsOfEditingAndEditingAreas(params).subscribe(
            (res) => {
                if (res.status == 1) {
                    let data = res.data.map((item) => {
                        return {
                            value: item.count,
                            name: this.getCityLabel(item.city),
                        };
                    });
                    // console.log(data);

                    this.leftBottomChartOption = getTwoPieOption();
                } else {
                    this.message.error(res.mag);
                }
            }
        );
    }
    //4、组织影响主机数统计
    getRightTopChartOption() {
        let params = {
            frequence: this.tiConsumeTime
        };
        this.service.get_department_statistics(params).subscribe(
            (res) => {
                // console.log(res);

                if (res.status == 1) {

                    // 生成图表配置
                    this.rightTopChartOption = getBarThreeOption();

                } else {
                    this.message.error(res.msg);
                }
            }
        );
    }

    //时间转换
    timeTrans() {
        let result = {
            start_time: 0,
            end_time: 0,
        }
        let index = 0;
        for (let item of this.dateRange) {
            if (index == 0) {
                result['start_time'] = parseInt(((new Date(item)).getTime() / 1000).toString())
            } else {
                result['end_time'] = parseInt(((new Date(item)).getTime() / 1000).toString())
            }
            index++
        }
        return result
    }
    ngOnInit() {
        setTimeout(() => {
            //中间1
            this.getCenterTopLeftChartOption()
            this.getCenterTopRightChartOption()
            this.getCenterBottomLeftChartOption()
            this.getCenterBottomRightChartOption()
            //
            this.getLeftTopChartOption();
            this.getLeftBottomChartOption();
            this.getRightBottomChartOption();
            this.getRightTopChartOption();
        }, 1000);
    }
}
