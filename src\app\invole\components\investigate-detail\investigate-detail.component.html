<div style="display: flex; gap: 10px; min-height: 100%">
    <div class="chat-area">
        <div class="header" style="display: flex; justify-content: space-between">
            <h3>{{ name }}</h3>

            <button nz-button (click)="goBack()" nzType="primary">返回</button>
        </div>
        <div *ngIf="listOfDataAll.length > 0">
            <div *ngFor="let item of listOfDataAll">
                <div class="chat-message">
                    <div class="message">
                        <span class="name" *ngIf="isGroup" nz-tooltip [nzTitle]="'用户名称：' + item.sender_name">
                            <span style="font-size: 14px; font-weight: 600">用户名称：</span>
                            {{ item.sender_name }}</span
                        >
                        <span class="name" *ngIf="!isGroup" nz-tooltip [nzTitle]="'群组名称：' + item.group_name">
                            <span style="font-size: 14px; font-weight: 600">群组名称：</span>
                            {{ item.group_name }}</span
                        >
                        <!-- <nz-divider nzType="vertical"></nz-divider> -->
                        <span>
                            <span style="font-size: 14px; font-weight: 600">时间：</span>
                            {{ item.send_time }}</span
                        >
                    </div>
                    <span style="font-size: 14px; font-weight: 600">内容：</span><br />
                    <pre style="margin-top: 16px; font-size: 15px">{{ item.content }}</pre>
                </div>
            </div>
            <!-- 分页器 -->
            <div class="pagination">
                <span style="border: none">共 {{ member_info_Total }} 条</span>

                <nz-pagination
                    [nzPageIndex]="isGroup ? params.page_num : member_info.page_num"
                    [nzTotal]="member_info_Total"
                    [nzPageSize]="isGroup ? params.page_size : member_info.page_size"
                    nzShowSizeChanger
                    (nzPageIndexChange)="onPageChange($event)"
                    (nzPageSizeChange)="onPageSizeChange($event)"
                ></nz-pagination>
            </div>
        </div>

        <nz-empty style="margin: 190px 8px" *ngIf="listOfDataAll.length <= 0"></nz-empty>
    </div>
    <!-- 群组信息区域 -->
    <div class="group-info" *ngIf="isGroup">
        <!-- 群组描述 -->
        <div class="group-description">
            <h5>群主</h5>
            <p>{{ group_basic_info_list.group_owner }}</p>
        </div>
        <div class="group-description">
            <h5>群组创建时间</h5>
            <p>{{ group_basic_info_list.create_time }}</p>
        </div>
        <div class="group-description">
            <h5>群组介绍</h5>
            <p>{{ group_basic_info_list.group_introduce }}</p>
        </div>

        <div class="group-description">
            <div style="display: flex; justify-content: space-between; align-items: center">
                <h5>群组标签</h5>
                <div>
                    <ng-container *ngIf="!group_basic_info_list.isEdit; else saveTpl">
                        <a (click)="editGroupLabel(group_basic_info_list)">编辑标签</a>
                    </ng-container>
                    <ng-template #saveTpl>
                        <a (click)="saveEdit(group_basic_info_list)" style="margin-right: 8px">保存</a>
                        <a nz-popconfirm nzTitle="确定取消吗?" [nzCancelText]="" (nzOnConfirm)="cancelEdit(group_basic_info_list)">取消</a>
                    </ng-template>
                </div>
            </div>

            <ng-container *ngIf="!group_basic_info_list.isEdit; else groupInputTpl">
                <nz-tag *ngFor="let tag of group_basic_info" nz-tooltip [nzTitle]="'群组标签：' + tag">{{ tag }}</nz-tag>
            </ng-container>
            <ng-template #groupInputTpl>
                <input placeholder="输入群组标签，用逗号分隔" type="text" nz-input [(ngModel)]="group_basic_info_list.group_label" />
            </ng-template>
        </div>
        <div class="group-description">
            <h5>群组类型</h5>
            <p>{{ group_basic_info_list.group_type }}</p>
        </div>
        <div class="group-description">
            <h5>成员数量</h5>
            <p>{{ group_basic_info_list.member_count }}</p>
        </div>

        <!-- 群组成员 -->
        <div class="group-members-section">
            <h5>群组成员</h5>
            <nz-table
                #basicMemberTable
                nzShowSizeChanger
                [nzData]="listOfData"
                [nzFrontPagination]="false"
                nzShowPagination
                [nzTotal]="group_member_Total"
                [nzShowTotal]="totalTemplate"
                [nzPageIndex]="group_info.pageNum"
                [nzPageSize]="group_info.pageSize"
                [nzBordered]="false"
                [nzPageSizeOptions]="[10, 20, 50, 100]"
                [nzShowQuickJumper]="false"
                (nzPageIndexChange)="groupMemberPageSizeChanged($event)"
                (nzPageSizeChange)="groupMemberPageChanged($event)"
            >
                <thead>
                    <tr>
                        <th>成员名</th>
                        <th>所在群组ID</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of basicMemberTable.data">
                        <td>{{ data.member_name }}</td>
                        <td>{{ data.group_ids }}</td>
                        <td>{{ data.create_time }}</td>
                        <td (click)="showPersonnelDetails(data)">
                            <a>查看人员详情</a>
                        </td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #totalTemplate let-total>共 {{ group_member_Total }} 条</ng-template>
        </div>
        <!-- 群词云分析 -->
        <div class="word-cloud-analysis">
            <h5>词频分析</h5>
            <div *ngIf="showWordCloud" echarts [options]="wordCloudChartOption" class="dash-chart"></div>
            <nz-empty style="margin: 30px 8px" *ngIf="!showWordCloud"></nz-empty>
        </div>
        <!-- 相似群组 -->
        <div class="similar-groups">
            <nz-table #basicTable [nzData]="similarGroups" [nzShowPagination]="false">
                <thead>
                    <tr>
                        <th>相似群组</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of basicTable.data">
                        <td>{{ data.group_name }}</td>
                    </tr>
                </tbody>
            </nz-table>
        </div>
    </div>

    <div class="group-info" *ngIf="!isGroup">
        <div class="group-tags">
            <div style="display: flex; justify-content: space-between; align-items: center">
                <h5>成员标签</h5>
                <div>
                    <ng-container *ngIf="!members_info.isEditMember; else saveTpl">
                        <a (click)="editLabel(members_info)">编辑标签</a>
                    </ng-container>
                    <ng-template #saveTpl>
                        <a (click)="saveEditUser(members_info)" style="margin-right: 8px">保存</a>
                        <a nz-popconfirm nzTitle="确定取消吗?"    
                     [nzCancelText]="null"    nzOkText="确定"   (nzOnConfirm)="cancelEditUser(members_info)">取消123</a>
                    </ng-template>
                   
                </div>
            </div>

            <ng-container *ngIf="!members_info.isEditMember; else groupInputTpl">
                <nz-tag *ngFor="let tag of Member_basic_info" nz-tooltip [nzTitle]="'成员标签：' + tag">{{ tag }}</nz-tag>
            </ng-container>
            <ng-template #groupInputTpl>
                <input placeholder="输入成员标签，用逗号分隔" type="text" nz-input [(ngModel)]="members_info.member_label" />
            </ng-template>
        </div>
        <div class="group-tags">
            <div style="display: flex; justify-content: space-between">
                <h5>成员姓名</h5>
            </div>
            <p>{{ members_info.last_name }} {{ members_info.first_name }}</p>
        </div>
        <!-- <div class="group-tags">
            <div style="display: flex; justify-content: space-between">
                <h5>成员名</h5>
            </div>
            <p>{{ members_info.first_name }}</p>
        </div> -->
        <div class="group-tags">
            <div style="display: flex; justify-content: space-between">
                <h5>成员电话</h5>
            </div>
            <p>{{ members_info.phone }}</p>
        </div>

        <div class="group-members-section">
            <h5>所属群组</h5>
            <nz-table #userBasicTable [nzData]="userListOfData">
                <thead>
                    <tr>
                        <th [nzWidth]="'20%'">群组名</th>
                        <th [nzWidth]="'20%'">成员数量</th>
                        <th [nzWidth]="'30%'">创建时间</th>
                        <th [nzWidth]="'30%'">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of userBasicTable.data">
                        <td nz-tooltip [nzTitle]="'群组名：' + data.group_name" style="max-width: 220px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis">
                            {{ data.group_name }}
                        </td>
                        <td>{{ data.member_count }}</td>

                        <td>{{ data.create_time }}</td>
                        <td (click)="showGroupDetails(data)">
                            <a>查看群组详情</a>
                        </td>
                    </tr>
                </tbody>
            </nz-table>
        </div>

        <div class="similar-groups">
            <nz-table #basicLocation [nzData]="suspectedLocation" [nzShowPagination]="false">
                <thead>
                    <tr>
                        <th>疑似位置</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of basicLocation.data">
                        <td>{{ data }}</td>
                    </tr>
                </tbody>
            </nz-table>
        </div>
    </div>
</div>
