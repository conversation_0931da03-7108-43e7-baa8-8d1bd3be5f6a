const { BundleAnalyzerPlugin } = require("webpack-bundle-analyzer");
const MomentLocalesPlugin = require("moment-locales-webpack-plugin");
const TerserPlugin = require("terser-webpack-plugin");

module.exports = {
    // output: {
    //     filename: "[name].bundle.js",
    // },
    plugins: [
        new BundleAnalyzerPlugin({
            analyzerMode: "static",
            openAnalyzer: true,
            reportFilename: "../bundle-report.html",
        }),
        new MomentLocalesPlugin({
            localesToKeep: ["en", "zh-cn"], // 只保留需要的语言
        }),
    ],
    optimization: {
        minimize: true,
        minimizer: [
            new TerserPlugin({
                terserOptions: {
                    output: {
                        ascii_only: false, // 保留非 ASCII 字符
                    },
                },
            }),
        ],
    },
};
