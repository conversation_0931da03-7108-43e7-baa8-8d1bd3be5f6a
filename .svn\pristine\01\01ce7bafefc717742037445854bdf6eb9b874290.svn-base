import { Component, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { HttpService } from 'app/core';
import { IndustryMappingServiceService } from 'app/core/service/industry-mapping-service.service';
import { $, namespace_produce as namespace } from 'app/shared';
import { EChartsOption } from "echarts/lib/echarts";

import 'echarts/lib/chart/line';
import 'echarts/lib/chart/bar';
import 'echarts/lib/chart/pie';
// component examples:
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/title';
import 'echarts/lib/component/legend';
import { ReportService } from "./services/report.service";
import { locationService } from "./services/location";
import { getPieOption, getBarThreeOption, getLineOption } from "./common/reports";
import { NzMessageService } from "ng-zorro-antd";
@Component({
  selector: 'app-shield-visual-suffer-injury',
  templateUrl: './shield-visual-suffer-injury.component.html',
  styleUrls: ['./shield-visual-suffer-injury.component.styl']
})
export class ShieldVisualSufferInjuryComponent implements OnInit {
  @ViewChild("mapLineJiangxiChartComponent", null) MapLineJiangxiChartComponent: any;
  mapData: any = [];
  typeTrendData = [];
  mapConfig: any = {
    title: "",
    eventType: [
      { name: "APT相关事件", color: "#FF3400", dataRange: [80, 100] },
      { name: "关基攻击团伙相关事件", color: "#FAC149", dataRange: [60, 80] },
      { name: "恶意代码家族相关事件", color: "#00FFE4", dataRange: [40, 60] }
    ],
    color: ["#00FFE4", "#FAC149", '#FF3400', '#68b92e'],
    dataRange: [40, 60, 80, 100],
    symbolSvgPath: ["#7ad8f5", "#f8f8a1", "#ffa3a3"],
    areaSymbolSize: 20
  };
  loading = {
    summary: false,
    process: false,
    source: false,
    category: false,
    keywords: false,
    wordCloud: false,
    topTopics: false,
    recommendTopics: false,
  };

  leftTopChartOption: EChartsOption;
  leftBottomChartOption: EChartsOption;
  rightTopChartOption: EChartsOption;
  rightBottomChartOption: EChartsOption;
  threatorActionData = []
  dateRange: any = [];
  intelligenceQuantityLoading: boolean = false;
  cityDate = []
  tiConsumeTime = '30';
  constructor(
    private http: HttpService,
    private router: Router,
    private industryMapping: IndustryMappingServiceService,
    private location: locationService,
    private message: NzMessageService,
    private service: ReportService,
    private cdr: ChangeDetectorRef

  ) {
    $(".container-box").removeClass("containerCustom");
  }
  ngOnDestroy() {
    $(".container-box").addClass("containerCustom");
  }

  //地图选择
  mapRequest(map) {
    this.mapData = JSON.parse(JSON.stringify(this.mapData))
  }

  //处理查询城市列表 listOfData.threat_type
  getCityLabel(id): string {
    const found = this.cityDate.find(item => item.id == id);
    return found ? found.name : '未知';
  }
  //1、失陷主机行业统计
  getLeftTopChartOption() {
    let params = {
      frequence: this.tiConsumeTime
    };
    this.service.get_department_statistics(params).subscribe(
      (res) => {
        // console.log(res);

        if (res.status == 1) {

          // 生成图表配置
          this.leftTopChartOption = getBarThreeOption();
        } else {
          this.message.error(res.msg);
        }
      }
    );
  }
  //3、组织影响主机数统计
  getRightBottomChartOption() {
    let params = {
      frequence: this.tiConsumeTime
    };
    this.service.get_department_statistics(params).subscribe(
      (res) => {
        // console.log(res);

        if (res.status == 1) {
          // 生成图表配置
          this.rightBottomChartOption = getLineOption();

        } else {
          this.message.error(res.msg);
        }
      }
    );
  }
  //2、失陷主机地区分布
  getLeftBottomChartOption() {
    let params = {
      frequence: this.tiConsumeTime
    }
    this.service.statisticsOfEditingAndEditingAreas(params).subscribe(
      (res) => {
        if (res.status == 1) {
          let data = res.data.map((item) => {
            return {
              value: item.count,
              name: this.getCityLabel(item.city),
            };
          });
          // console.log(data);

          this.leftBottomChartOption = getPieOption('', data);
        } else {
          this.message.error(res.mag);
        }
      }
    );
  }
  //4、组织影响主机数统计
  getRightTopChartOption() {
    let params = {
      frequence: this.tiConsumeTime
    };
    this.service.get_department_statistics(params).subscribe(
      (res) => {
        // console.log(res);

        if (res.status == 1) {

          // 生成图表配置
          this.rightTopChartOption = getBarThreeOption();

        } else {
          this.message.error(res.msg);
        }
      }
    );
  }
  //地图数据二次封装
  mapDataTrans(data) {
    let result = [];
    let index = 0;
    if (data.length != 0) {
      for (let item of data) {
        result.push({
          sourceName: item.src_country == '中国' ? (item.src_city == '' ? (item.src_province == "" ? item.src_country : item.src_province) : item.src_city.split('市')[0]) : item.src_country,
          targetName: item.dst_country == '中国' ? (item.dst_city == '' ? (item.dst_province == "" ? item.dst_country : item.dst_province) : item.dst_city.split('市')[0]) : item.dst_country,
          value: item.event_type == "关基攻击团伙相关事件" ? 70 : (item.event_type == "恶意代码家族相关事件" ? 50 : 90),
          customTitle: item.event_type
        })
      }
    }
    return result
  }
  //时间转换
  timeTrans() {
    let result = {
      start_time: 0,
      end_time: 0,
    }
    let index = 0;
    for (let item of this.dateRange) {
      if (index == 0) {
        result['start_time'] = parseInt(((new Date(item)).getTime() / 1000).toString())
      } else {
        result['end_time'] = parseInt(((new Date(item)).getTime() / 1000).toString())
      }
      index++
    }
    return result
  }
  hotspot: any = ""
  //地图数据
  getMapData() {
    let send = {
    }
    send = this.timeTrans()
    send["type"] = "all"
    if (this.hotspot[0] != "") {
      send['hot'] = this.hotspot
    }
    this.http.post({ url: namespace + "visual/getAptAttackTarget/", params: send, data_type: "FormData" }).then(res => {
      if (res.status == 1) {
        // res={"status": 1, "msg": "执行成功！", "data": [{"src_city": "FrankfurtamMain", "event_type": "恶意代码家族相关事件", "src_province": "Hessen", "dst_province": "England", "dst_country": "英国", "num": 470, "dst_city": "London", "src_country": "德国"}, {"src_city": "Trollhattan", "event_type": "恶意代码家族相关事件", "src_province": "Vastra Gotalands lan", "dst_province": "Quebec", "dst_country": "加拿大", "num": 353, "dst_city": "Mascouche", "src_country": "瑞典"}, {"src_city": "Trollhattan", "event_type": "关基攻击团伙相关事件", "src_province": "Vastra Gotalands lan", "dst_province": "Virginia", "dst_country": "美国", "num": 30, "dst_city": "Herndon", "src_country": "瑞典"}, {"src_city": "Clearwater", "event_type": "关基攻击团伙相关事件", "src_province": "Florida", "dst_province": "Hessen", "dst_country": "德国", "num": 27, "dst_city": "FrankfurtamMain", "src_country": "美国"}, {"src_city": "Reston", "event_type": "关基攻击团伙相关事件", "src_province": "Virginia", "dst_province": "Hessen", "dst_country": "德国", "num": 11, "dst_city": "FrankfurtamMain", "src_country": "美国"}, {"src_city": "Lecanto", "event_type": "关基攻击团伙相关事件", "src_province": "Florida", "dst_province": "Hessen", "dst_country": "德国", "num": 6, "dst_city": "FrankfurtamMain", "src_country": "美国"}, {"src_city": "Austin", "event_type": "关基攻击团伙相关事件", "src_province": "Texas", "dst_province": "Hessen", "dst_country": "德国", "num": 6, "dst_city": "FrankfurtamMain", "src_country": "美国"}, {"src_city": "Brandon", "event_type": "关基攻击团伙相关事件", "src_province": "Florida", "dst_province": "Hessen", "dst_country": "德国", "num": 5, "dst_city": "FrankfurtamMain", "src_country": "美国"}, {"src_city": "Reston", "event_type": "关基攻击团伙相关事件", "src_province": "Virginia", "dst_province": "Bucuresti", "dst_country": "罗马尼亚", "num": 5, "dst_city": "Bucharest", "src_country": "美国"}, {"src_city": "北京市", "event_type": "关基攻击团伙相关事件", "src_province": "北京市", "dst_province": "北京市", "dst_country": "中国", "num": 4, "dst_city": "北京市", "src_country": "中国"}, {"src_city": "Reston", "event_type": "关基攻击团伙相关事件", "src_province": "Virginia", "dst_province": "Hunedoara", "dst_country": "罗马尼亚", "num": 4, "dst_city": "Deva", "src_country": "美国"}, {"src_city": "Auburndale", "event_type": "关基攻击团伙相关事件", "src_province": "Florida", "dst_province": "Hessen", "dst_country": "德国", "num": 3, "dst_city": "FrankfurtamMain", "src_country": "美国"}, {"src_city": "Reston", "event_type": "关基攻击团伙相关事件", "src_province": "Virginia", "dst_province": "Alberta", "dst_country": "加拿大", "num": 2, "dst_city": "Calgary", "src_country": "美国"}, {"src_city": "Reston", "event_type": "关基攻击团伙相关事件", "src_province": "Virginia", "dst_province": "Florida", "dst_country": "美国", "num": 2, "dst_city": "Melbourne", "src_country": "美国"}, {"src_city": "Reston", "event_type": "关基攻击团伙相关事件", "src_province": "Virginia", "dst_province": "England", "dst_country": "英国", "num": 1, "dst_city": "Watford", "src_country": "美国"}, {"src_city": "Madrid", "event_type": "关基攻击团伙相关事件", "src_province": "Madrid, Comunidad de", "dst_province": "New Jersey", "dst_country": "美国", "num": 1, "dst_city": "Clifton", "src_country": "西班牙"}, {"src_city": "香港特别行政区", "event_type": "关基攻击团伙相关事件", "src_province": "香港特别行政区", "dst_province": "New Jersey", "dst_country": "美国", "num": 1, "dst_city": "Clifton", "src_country": "中国"}, {"src_city": "Stockholm", "event_type": "关基攻击团伙相关事件", "src_province": "Stockholms lan", "dst_province": "福建省", "dst_country": "中国", "num": 1, "dst_city": "厦门市", "src_country": "瑞典"}, {"src_city": "RoundRock", "event_type": "关基攻击团伙相关事件", "src_province": "Texas", "dst_province": "Hessen", "dst_country": "德国", "num": 1, "dst_city": "FrankfurtamMain", "src_country": "美国"}, {"src_city": "Plano", "event_type": "关基攻击团伙相关事件", "src_province": "Texas", "dst_province": "Hessen", "dst_country": "德国", "num": 1, "dst_city": "FrankfurtamMain", "src_country": "美国"}, {"src_city": "Stockholm", "event_type": "APT相关事件", "src_province": "Stockholms lan", "dst_province": "上海市", "dst_country": "中国", "num": 3033, "dst_city": "上海市", "src_country": "瑞典"}, {"src_city": "SanDiego", "event_type": "APT相关事件", "src_province": "California", "dst_province": "上海市", "dst_country": "中国", "num": 908, "dst_city": "上海市", "src_country": "美国"}, {"src_city": "Stockholm", "event_type": "APT相关事件", "src_province": "Stockholms lan", "dst_province": "浙江省", "dst_country": "中国", "num": 364, "dst_city": "宁波市", "src_country": "瑞典"}, {"src_city": "Stockholm", "event_type": "APT相关事件", "src_province": "Stockholms lan", "dst_province": "北京市", "dst_country": "中国", "num": 283, "dst_city": "北京市", "src_country": "瑞典"}, {"src_city": "Stockholm", "event_type": "APT相关事件", "src_province": "Stockholms lan", "dst_province": "福建省", "dst_country": "中国", "num": 141, "dst_city": "厦门市", "src_country": "瑞典"}, {"src_city": "SanDiego", "event_type": "APT相关事件", "src_province": "California", "dst_province": "浙江省", "dst_country": "中国", "num": 90, "dst_city": "宁波市", "src_country": "美国"}, {"src_city": "Stockholm", "event_type": "APT相关事件", "src_province": "Stockholms lan", "dst_province": "山东省", "dst_country": "中国", "num": 44, "dst_city": "泰安市", "src_country": "瑞典"}, {"src_city": "Paris", "event_type": "APT相关事件", "src_province": "Ile-de-France", "dst_province": "上海市", "dst_country": "中国", "num": 40, "dst_city": "上海市", "src_country": "法国"}, {"src_city": "Milan", "event_type": "APT相关事件", "src_province": "Lombardia", "dst_province": "上海市", "dst_country": "中国", "num": 34, "dst_city": "上海市", "src_country": "意大利"}, {"src_city": "LosAngeles", "event_type": "APT相关事件", "src_province": "California", "dst_province": "上海市", "dst_country": "中国", "num": 34, "dst_city": "上海市", "src_country": "美国"}, {"src_city": "KualaLumpur", "event_type": "APT相关事件", "src_province": "Wilayah Persekutuan Kuala Lumpur", "dst_province": "湖北省", "dst_country": "中国", "num": 24, "dst_city": "武汉市", "src_country": "马来西亚"}, {"src_city": "Portland", "event_type": "APT相关事件", "src_province": "Oregon", "dst_province": "上海市", "dst_country": "中国", "num": 19, "dst_city": "上海市", "src_country": "美国"}, {"src_city": "SanDiego", "event_type": "APT相关事件", "src_province": "California", "dst_province": "福建省", "dst_country": "中国", "num": 18, "dst_city": "厦门市", "src_country": "美国"}, {"src_city": "SanDiego", "event_type": "APT相关事件", "src_province": "California", "dst_province": "北京市", "dst_country": "中国", "num": 17, "dst_city": "北京市", "src_country": "美国"}, {"src_city": "Stockholm", "event_type": "APT相关事件", "src_province": "Stockholms lan", "dst_province": "福建省", "dst_country": "中国", "num": 15, "dst_city": "福州市", "src_country": "瑞典"}, {"src_city": "Manassas", "event_type": "APT相关事件", "src_province": "Virginia", "dst_province": "上海市", "dst_country": "中国", "num": 13, "dst_city": "上海市", "src_country": "美国"}, {"src_city": "Fremont", "event_type": "APT相关事件", "src_province": "California", "dst_province": "上海市", "dst_country": "中国", "num": 13, "dst_city": "上海市", "src_country": "美国"}, {"src_city": "Stockholm", "event_type": "APT相关事件", "src_province": "Stockholms lan", "dst_province": "广东省", "dst_country": "中国", "num": 12, "dst_city": "广州市", "src_country": "瑞典"}, {"src_city": "Bucharest", "event_type": "APT相关事件", "src_province": "Bucuresti", "dst_province": "上海市", "dst_country": "中国", "num": 12, "dst_city": "上海市", "src_country": "罗马尼亚"}, {"src_city": "Paris", "event_type": "APT相关事件", "src_province": "Ile-de-France", "dst_province": "北京市", "dst_country": "中国", "num": 10, "dst_city": "北京市", "src_country": "法国"}]}
        this.mapData = this.mapDataTrans(res.data)
      }
    })
  }

  ngOnInit() {
    setTimeout(() => {
      // this.getMapData();
      this.getLeftTopChartOption();
      this.getLeftBottomChartOption();
      this.getRightBottomChartOption();
      this.getRightTopChartOption();
    }, 1000); // 延迟 1 秒执行
  }
}
