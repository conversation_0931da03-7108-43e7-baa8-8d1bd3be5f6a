import { Component, OnInit } from "@angular/core";
import { ReportService } from "../../services/report.service";
import { NzMessageService, NzDrawerService, NzModalService } from "ng-zorro-antd";
import { ReportAddUpdateComponent } from "../../components/report-add-update/report-add-update.component";
import { Router } from '@angular/router';
import { DocxPreviewComponent } from "../../../file-preview/docx-preview/docx-preview.component";
import { PdfPreviewComponent } from "../../../file-preview/pdf-preview/pdf-preview.component";
import { HttpResponse } from '@angular/common/http';
import { locationService } from "../../services/location";
import { namespace, namespace_manage } from "app/shared";
import { HttpClient } from "@angular/common/http";

@Component({
    selector: 'app-report',
    templateUrl: './report.component.html',
    styleUrls: ['./report.component.styl']
})
export class ReportComponent implements OnInit {

    // values: any[] = ['360000', '360100', '360102'];
    listOfData = []
    total = 0
    intelligenceTypeData = []
    leadTypeData = []
    threat_typeData = []
    cityDate = []
    jobLabelData = []//业务标签
    dateRange = []; // 保存选择的日期范围
    loading = {
        table: false,
        delete: false,
    }
    searchData = {
        intelligenceType: this.intelligenceTypeData,
        cluesType: this.leadTypeData,
        status: "",
        task_type: "",
        city: [],
        keyword: "",
        create_time: "",
        page: 1,
        size: 10,
    }


    constructor(
        private http: HttpClient,
        private location: locationService,
        private modal: NzModalService,
        private router: Router, // 路由服务，用于导航
        private message: NzMessageService,
        private service: ReportService,
        private drawerService: NzDrawerService, // 抽屉服务，用于打开编辑界面
        private modalService: NzModalService // 使用 NzModalService 显示预览
    ) {
    }

    ngOnInit() {
        this.getPermissions()
        this.getCityDate()
        this.getData().then(() => {
            this.initializeSearchData();
        });
        this.getQueryStatus()
        this.searchHandler()
    }
    //获取查询状态
    queryStatus = []
    getQueryStatus() {
        this.service.getQueryStatus({}).subscribe(
            (data) => {
                // console.log(data);
                this.queryStatus = [{ status: "全部", id: "" }, ...data.data];
            }
        );
    }
    //获取权限
    permissions: any
    getPermissions() {
        this.service.getPermissions({}).subscribe(
            (data) => {
                this.permissions = data.data.control
                // console.log("11111", this.permissions);
            });
    }
    //报告列表点击获取详情
    report_search(data) {
        let params = {
            id: data.task_id
        };
        this.service.report_search(params).subscribe((res) => {
            if (res.status === 1) {
                // 弹窗展示查询到的报告详情
                this.modal.info({
                    nzTitle: '报告详情',
                    nzContent: `
                    <div><strong>ID：</strong> ${res.data.id}</div>
                    <div><strong>名称：</strong> ${res.data.task_name}</div>
                    <div><strong>描述：</strong> ${res.data.description}</div>
                    <div><strong>时间：</strong> ${res.data.start_time}-${res.data.end_time}</div>
                `,
                    nzOkText: '关闭',
                    nzWidth: '600px'  // 设置弹窗宽度
                });
            } else {
                this.message.error(res.msg);
            }
        });
    }
    //地区的值
    onChanges(selectedValues: any) {
        // console.log('选中的区域:', selectedValues);
        this.searchData.city = selectedValues
    }

    // 异步赋值
    async getData() {
        this.threat_typeData = this.location.threat_typeData;
        this.intelligenceTypeData = this.location.intelligenceTypeData;
        this.leadTypeData = this.location.leadTypeData;
        this.jobLabelData = this.location.jobLabelData;
    }
    initializeSearchData() {
        const resetIntelligenceType = this.intelligenceTypeData.map(item => ({
            ...item,
            checked: false
        }));

        const resetLeadType = this.leadTypeData.map(item => ({
            ...item,
            checked: false
        }));

        this.searchData = {
            intelligenceType: resetIntelligenceType,
            cluesType: resetLeadType,
            status: "",
            task_type: "",
            city: [],
            keyword: "",
            create_time: "",
            page: 1,
            size: 10,
        };
    }


    //处理搜索的数据
    formatterData(data: any) {
        if (!data || !Array.isArray(data.intelligenceType) || !Array.isArray(data.cluesType)) {
            console.error("formatterData received invalid data:", data);
            return {};
        }

        return {
            intelligenceType: data.intelligenceType
                .filter(item => item.checked)
                .map(item => item.value), // 只保留 value

            cluesType: data.cluesType
                .filter(item => item.checked)
                .map(item => item.value), // 只保留 value

            threator_location: data.threator_location,
            keyword: data.keyword,
            create_time: data.create_time,
            task_type: data.task_type,
            city: data.city,
            status: data.status,
            page: data.page,
            size: data.size
        };
    }

    //日期变化函数
    onChange(result: Date[]): void {
        const convertToUnixTimestamp = (date: Date): number => {
            return Math.floor(date.getTime() / 1000); // 将日期转换为秒级 UNIX 时间戳
        };

        const formattedDates = result.map(date => convertToUnixTimestamp(new Date(date))); // 转换日期范围
        // console.log(formattedDates); // 打印时间戳数组

        this.searchData.create_time = formattedDates.join(','); // 将时间戳数组拼接成字符串
    }

    //查询报告列表
    searchHandler() {
        this.loading.table = true
        // this.searchData.threator_location = this.mapLocationValues(this.values);
        const filteredData = this.formatterData(this.searchData);
        this.service.getReportData(filteredData).subscribe(
            (data) => {
                if (data.status == 1) {
                    this.listOfData = data.data.data
                    this.total = data.data.count
                    this.loading.table = false
                } else {
                    this.message.error(data.msg);
                    this.listOfData = []
                    this.total = 0
                    this.loading.table = false
                }
            });
    }

    //处理查询报告列表 listOfData.threat_type
    getThreatTypeLabel(type: number): string {
        const list = this.threat_typeData;
        if (!list || !Array.isArray(list)) {
            return '未知';
        }
        const found = list.find(item => item.value == type);
        return found ? found.label : '未知';
    }


    //处理查询城市列表 listOfData.threat_type
    getCityLabel(id: number): string {
        const list = this.cityDateAll;
        if (!list || !Array.isArray(list)) {
            return '未知';
        }
        const found = list.find(item => item.id == id);
        return found ? found.name : '未知';
    }

    getCityChildrenLabel(id: number): string {
        const list = this.cityChildrenArray;
        if (!list || !Array.isArray(list)) {
            return '';
        }
        const found = list.find(item => item.id == id);
        return found ? ` ${found.name}` : '';
    }


    //页码变化处理
    changePageIndex(newPageIndex: number): void {
        this.searchData.page = newPageIndex;
        this.searchHandler(); // 假设你有一个 loadData 方法来加载新页数据
    }

    changePageSize(size: number) {
        this.searchData.size = size;
        this.searchData.page = 1; // 重置为第一页
        this.searchHandler(); // 重新查询数据
    }



    // 添加或修改报告的抽屉打开方法---------------------------------------------------------------------------------------------
    report(data?: any) {
        // console.log(data);

        const drawerRef = this.drawerService.create({
            nzTitle: '成品报告上报',  // 根据是否有data判断标题
            nzWidth: 800,
            nzContent: ReportAddUpdateComponent,
            nzContentParams: {
                data: data || null,  // 传递配置数据，data存在则为修改，不存在则为添加
                // configuration: this.configuration || null,  // 传递配置数据，data存在则为修改，不存在则为添加
                cityDate: this.cityDate || null,  // 传递配置数据，data存在则为修改，不存在则为添加
                reportInquiry: this.searchHandler.bind(this) // 传递获取报告的方法
            }
        });

        drawerRef.afterClose.subscribe(() => {

            // this.router.navigate(['entity-expansion/report']);  // 关闭抽屉后返回权重配置页面
        });
    }

    // 处理报告录入按钮点击，触发添加报告
    addReport() {
        this.report();  // 不传递data，表示添加操作
    }


    //表格内的操作
    handleAction(actionId: string, actionName: string, data: any) {
        switch (actionId) {
            case '7':
                this.handleModifyReport(data);
                break;
            case '9':
                this.handleEdit(actionId, actionName, data);
                break;
            default:
                this.handleEdit(actionId, actionName, data);
        }
    }

    //修改流程
    modifyTheProcess(actionId, actionName, data) {
        let params = {
            id: data.id,
            status: actionId
        }
        this.service.modifyTheProcess(params).subscribe(
            (res) => {
                if (res.status == 1) {
                    this.message.success(res.msg);
                    this.searchHandler()
                } else {
                    this.message.error(res.msg);
                }
            }
        );
    }

    handleEdit(actionId, actionName, data) {
        this.modal.confirm({
            nzTitle: `确定要${actionName}报告《${data.title}》吗？`,
            nzOkText: '确认',
            nzCancelText: '取消',
            nzOnOk: () => this.modifyTheProcess(actionId, actionName, data) // 只有点击确认才会执行采编
        });
    }

    handleModifyReport(data: any) {
        this.report(data);
    }

    //下载报告

    handleDownloadReport(data: any) {
        this.modal.confirm({
            nzTitle: `确定要下载报告《${data.title}》吗？`,
            nzOkText: '确认',
            nzCancelText: '取消',
            nzOnOk: () => this.confirmDownloadReport(data) // 只有点击确认才会执行撤销
        });
    }

    confirmDownloadReport(data) {
        const url = `${namespace_manage}command_dispatch/download_report/`;
        const params = { id: data.id };

        this.http.get(url, {
            params: params,
            responseType: 'blob',
            observe: 'response'
        }).subscribe((response) => {
            const contentDisposition = response.headers.get('Content-Disposition');
            let fileName = data.report_name || 'downloaded_file';
            // console.log(response);

            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
                if (filenameMatch && filenameMatch[1]) {
                    fileName = decodeURIComponent(filenameMatch[1]);
                }
            }
            const blob = new Blob([response.body], { type: response.body ? response.body.type : 'application/octet-stream' });
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(downloadUrl);
        }, (error: any) => {
            console.error('文件下载失败:', error);
        });
    }

    //获取cityDate城市数据
    cityDateAll = []
    cityArray: any
    cityChildrenArray: any
    getCityDate() {
        this.service.getCityDate({}).subscribe((data) => {
            // console.log("API 响应数据:", data.data);
            this.cityDate = this.transformCityDate(data.data);
            this.cityDateAll = data.data
            this.cityArray = this.extractTopLevelCityValues(this.cityDate); // 预先存好父级城市 ID
            this.cityChildrenArray = data.data.flatMap(item => item.children);
            // console.log("处理后的城市数据:", this.cityDate);
            // console.log("父级城市 ID:", this.cityArray);
            // console.log("子级城市 ID:", this.cityChildrenArray);
        });
    }

    // 只提取最上层（父级）城市的 value
    extractTopLevelCityValues(cityData: any[]): number[] {
        return cityData.map(item => item.value);  // 仅获取父级项的 value
    }

    transformCityDate(cityData: any[]): any[] {
        return cityData.map(item => ({
            label: item.name,   // 城市名
            value: item.id,     // 城市ID
            children: item.children && item.children.length > 0
                ? item.children.map(child => ({
                    label: child.name,  // 县名
                    value: child.id,    // 县ID
                    isLeaf: true        // 标记为叶子节点
                }))
                : [],  // 没有子项时，确保设置为空数组
            // isLeaf: item.children && item.children.length === 0 // 判断是否是叶子节点
        }));
    }


    // 在选择变更时判断是否允许
    changeOnFunction = (option, _index): boolean => {
        if (!this.cityDate) {
            console.warn("cityDate 还未加载完成");
            return false;
        }

        const value = option.value;
        this.cityArray = this.extractTopLevelCityValues(this.cityDate);
        return this.cityArray.indexOf(value) >= 0;
    };

}