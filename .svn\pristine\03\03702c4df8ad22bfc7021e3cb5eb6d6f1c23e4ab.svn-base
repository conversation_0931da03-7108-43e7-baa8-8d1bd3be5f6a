<!-- 事件 -->
<div>
  <!-- 事件名称 -->
  <div nz-col nzSpan="24">
    <nz-form-item>
      <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
        <span class="required">*</span>名称
      </nz-form-label>
      <nz-form-control style="float: left;width: 75%;">
        <input nz-input [(ngModel)]="addData.name" placeholder="请输入事件名称" />
      </nz-form-control>
    </nz-form-item>
  </div>
  <!-- 来源名称 -->
  <div nz-col nzSpan="24">
    <nz-form-item>
      <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
        <span class="required">*</span>情报源
      </nz-form-label>
      <nz-form-control style="float: left;width: 75%;">
        <nz-select style="width: 100%" [(ngModel)]="addData.source_name" nzPlaceHolder="请选择来源名称">
          <nz-option *ngFor="let option of configuration.source_name_list" [nzLabel]="option"
            [nzValue]="option"></nz-option>
        </nz-select>
      </nz-form-control>
    </nz-form-item>
  </div>
  <!-- 事件描述 -->
  <div nz-col nzSpan="24">
    <nz-form-item>
      <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
        <span class="required">*</span>描述
      </nz-form-label>
      <nz-form-control style="float: left;width: 75%;">
        <textarea rows="4" nz-input [(ngModel)]="addData.description" placeholder="请输入事件描述"></textarea>
      </nz-form-control>
    </nz-form-item>
  </div>
</div>

<!-- IOC相关列表 -->
<nz-divider nzText="关联的IOC" nzOrientation="left"></nz-divider>
<div>
  <div *ngFor="let ioc of addData.ioc_related_list; let i = index">
    <!-- IOC相关列表 类别 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>IOC类型
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-select style="width: 100%" nzPlaceHolder="请选择IOC分类" [(ngModel)]="addData.ioc_related_list[i].category"
            (ngModelChange)="iOCClassification($event)">
            <nz-option *ngFor="let option of configuration.indicator_type_list" [nzLabel]="option" [nzValue]="option">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- IOC相关列表 IOC 值 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>可观察数据值
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <input nz-input [(ngModel)]="addData.ioc_related_list[i].ioc_value" placeholder="请输入可观察数据值,sample类型仅支持md5" />
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- IOC相关列表 威胁级别 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>威胁等级
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-select style="width: 100%" nzPlaceHolder="请选择IOC威胁级别"
            [(ngModel)]="addData.ioc_related_list[i].threat_level">
            <nz-option *ngFor="let option of configuration.indicator_threat_level_list" [nzLabel]="option.display"
              [nzValue]="option.pass">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- IOC相关列表 威胁类型 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>威胁类型
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-select style="width: 100%" nzPlaceHolder="请选择IOC威胁类型"
            [(ngModel)]="addData.ioc_related_list[i].threat_type">
            <nz-option *ngFor="let option of indicator_type_list" [nzLabel]="option.display" [nzValue]="option.pass">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- IOC相关列表 涉及行业 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          针对的行业
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-select style="width: 100%" nzPlaceHolder="请选择IOC涉及行业" [(ngModel)]="addData.ioc_related_list[i].industries"
            (ngModelChange)="onIndustriesChange($event, i)" nzMode="multiple">
            <nz-option *ngFor="let option of configuration.indicator_industry_list" [nzLabel]="option.display"
              [nzValue]="option.pass">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- IOC相关列表 创建时间 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>录入时间
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-date-picker [(ngModel)]="addData.ioc_related_list[i].created"
            (ngModelChange)="onDateChangeCreation($event, i)" [nzFormat]="dateFormat" nzShowTime placeholder="请选择创建时间">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- IOC相关列表 有效期 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          过期时间
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-date-picker [(ngModel)]="addData.ioc_related_list[i].valid_until"
            (ngModelChange)="onDateChangeIOCValidityPeriod($event, i)" [nzFormat]="dateFormat" nzShowTime
            nzPlaceHolder="默认不过期">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </div>

  </div>
  <div>
    <button style="width: 45%; margin-left: 5%;" (click)="removeIOCRelatedList()"
      [disabled]="addData.ioc_related_list.length <= 0" nz-button nzType="default"><i nz-icon nzType="minus"
        style="margin:auto;margin-top: 0px;" nzTheme="outline"></i></button>
    <button style="width: 45%;" (click)="addIOCRelatedList()" nz-button nzType="primary"><i nz-icon nzType="plus"
        style="margin:auto;margin-top: 0px;" nzTheme="outline"></i></button>
  </div>
</div>


<!-- 漏洞相关列表 -->
<nz-divider nzText="关联的漏洞" nzOrientation="left"></nz-divider>
<div>
  <div *ngFor="let vul of addData.vul_related_list; let i = index">
    <!-- 漏洞相关列表 漏洞名称 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>漏洞名称
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <input nz-input [(ngModel)]="addData.vul_related_list[i].name" placeholder="请输入漏洞名称" />
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- 漏洞相关列表 录入日期 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>录入日期
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-date-picker [(ngModel)]="addData.vul_related_list[i].time"
            (ngModelChange)="onDateChangeVulnerability($event, i)" [nzFormat]="dateFormat" nzShowTime
            placeholder="请选择漏洞录入日期">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- 漏洞相关列表 描述 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span> 描述
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <textarea rows="4" nz-input [(ngModel)]="addData.vul_related_list[i].description"
            placeholder="请输入漏洞描述"></textarea>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- 漏洞相关列表 威胁等级 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>威胁等级
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-select style="width: 100%" nzPlaceHolder="请选择威胁等级" [(ngModel)]="addData.vul_related_list[i].risk_level">
            <nz-option *ngFor="let option of configuration.vul_config.risk_level_list" [nzLabel]="option"
              [nzValue]="option">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- 漏洞相关列表 漏洞编号 -->
    <div nz-col nzSpan="24">
      <!-- 漏洞相关列表 漏洞编号 -->
      <div *ngFor="let item of addData.vul_related_list[i].bugNumbers; let bugIndex = index">
        <nz-form-item>
          <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
            漏洞编号
          </nz-form-label>
          <nz-form-control style="float: left;width: 75%;">
            <nz-select style="width: 25%; margin-right: 1%;" nzPlaceHolder="选择编号类型" [(ngModel)]="item.selectedKey"
              (ngModelChange)="onOptionChange($event, i, bugIndex)">
              <nz-option *ngFor="let option of configuration.vul_config.ids_list" [nzLabel]="option" [nzValue]="option">
              </nz-option>
            </nz-select>
            <input nz-input style="width: 61%; margin-right: 1%;" [(ngModel)]="item.bugNumber"
              (ngModelChange)="onBugNumberChange($event, i, bugIndex)" placeholder="请输入漏洞编号" />
            <button nz-button type="button" nzType="default" class="vul_btn" (click)="removeBugNumber(i, bugIndex)"
              [disabled]="addData.vul_related_list[i].bugNumbers.length === 1">
              <i nz-icon nzType="minus" nzTheme="outline"></i>
            </button>
          </nz-form-control>
        </nz-form-item>
      </div>
      <button nz-button type="button" nzType="default" class="vul_btn" (click)="addBugNumber(i)"
        style="position: relative; top: -46px; left: 644px;">
        <i nz-icon nzType="plus" nzTheme="outline"></i>
      </button>
    </div>

    <!-- 漏洞相关列表 是否已知 -->
    <div nz-col nzSpan="24" style="margin-top: -40px; z-index: 9;">
      <nz-form-item>
        <nz-form-label style="float: left; width: 15%; margin-right: 10px; text-align: right;">
          是否已知
        </nz-form-label>
        <nz-form-control style="float: left; width: 75%;">
          <nz-radio-group [(ngModel)]="addData.vul_related_list[i].is_known">
            <label nz-radio [nzValue]="true">是</label>
            <label nz-radio [nzValue]="false">否</label>
          </nz-radio-group>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <div>
    <button style="width: 45%; margin-left: 5%;" (click)="removeVulRelatedList()"
      [disabled]="addData.vul_related_list.length <= 0" nz-button nzType="default"><i nz-icon nzType="minus"
        style="margin:auto;margin-top: 0px;" nzTheme="outline"></i></button>
    <button style="width: 45%;" (click)="addVulRelatedList()" nz-button nzType="primary"><i nz-icon nzType="plus"
        style="margin:auto;margin-top: 0px;" nzTheme="outline"></i></button>
  </div>

</div>


<!-- APT相关列表 -->
<nz-divider nzText="关联的APT" nzOrientation="left"></nz-divider>
<div>
  <div *ngFor="let apt of addData.apt_related_list; let i = index">

    <!-- APT相关列表 APT名称 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>名称
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <input nz-input [(ngModel)]="addData.apt_related_list[i].name" placeholder="请输入APT名称" />
        </nz-form-control>
      </nz-form-item>
    </div>


    <!-- APT相关列表 APT分类 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>分类
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-select style="width: 100%" nzPlaceHolder="请选择APT分类" [(ngModel)]="addData.apt_related_list[i].categories"
            nzMode="multiple">
            <nz-option *ngFor="let option of configuration.apt_config" [nzLabel]="option.display"
              [nzValue]="option.pass">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- APT相关列表 时间 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>发现时间
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-date-picker [(ngModel)]="addData.apt_related_list[i].time" (ngModelChange)="onDateChangeAPT($event, i)"
            [nzFormat]="dateFormat" nzShowTime placeholder="请选择APT时间">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- APT相关列表 APT别名 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          别名
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <input nz-input [(ngModel)]="addData.apt_related_list[i].aliases" placeholder="请输入别名" />
        </nz-form-control>
      </nz-form-item>
    </div>

  </div>
  
  <div>
    <button style="width: 45%; margin-left: 5%;" (click)="removeAptRelatedList()"
      [disabled]="addData.apt_related_list.length <= 0" nz-button nzType="default"><i nz-icon nzType="minus"
        style="margin:auto;margin-top: 0px;" nzTheme="outline"></i></button>
    <button style="width: 45%;" (click)="addAptRelatedList()" nz-button nzType="primary"><i nz-icon nzType="plus"
        style="margin:auto;margin-top: 0px;" nzTheme="outline"></i></button>
  </div>
</div>


<!-- 家族相关列表 -->
<nz-divider nzText="关联的家族" nzOrientation="left"></nz-divider>
<div>
  <div *ngFor="let family of addData.malware_family_related_list; let i = index">
    <!-- 家族分类 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>分类
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-select style="width: 100%" nzPlaceHolder="请选择家族分类"
            [(ngModel)]="addData.malware_family_related_list[i].categories" nzMode="multiple">
            <nz-option *ngFor="let option of configuration.malware_family_categories_list" [nzLabel]="option.display"
              [nzValue]="option.pass">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- 家族名称 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>名称
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <input nz-input [(ngModel)]="addData.malware_family_related_list[i].name" placeholder="请输入家族名称" />
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- 家族别名 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          别名
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <input nz-input [(ngModel)]="addData.malware_family_related_list[i].aliases" placeholder="请输入家族别名（逗号分隔）"
            (blur)="onAliasesChangeFamily(family.aliasesInput, i)" />
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- 家族描述 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          <span class="required">*</span>描述
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <textarea rows="4" nz-input [(ngModel)]="addData.malware_family_related_list[i].description"
            placeholder="请输入家族描述"></textarea>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- 首次发现时间 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          发现时间
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <nz-date-picker [(ngModel)]="addData.malware_family_related_list[i].first_seen"
            (ngModelChange)="onDateChangeFirst($event, i)" [nzFormat]="dateFormat" nzShowTime placeholder="请选择首次发现时间">
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </div>
 
    <!-- 目标平台 -->
    <div nz-col nzSpan="24">
      <nz-form-item>
        <nz-form-label style="float: left;width: 15%;margin-right: 10px;text-align: right;">
          目标平台
        </nz-form-label>
        <nz-form-control style="float: left;width: 75%;">
          <input nz-input [(ngModel)]="addData.malware_family_related_list[i].target_platforms"
            placeholder="请输入目标平台（逗号分隔）" (blur)="onTargetPlatformsChange($event, i)" />
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
  <div>
    <button style="width: 45%; margin-left: 5%;" (click)="removeMalwareFamilyRelatedList()"
      [disabled]="addData.malware_family_related_list.length <= 0" nz-button nzType="default"><i nz-icon nzType="minus"
        style="margin:auto;margin-top: 0px;" nzTheme="outline"></i></button>
    <button style="width: 45%;" (click)="addMalwareFamilyRelatedList()" nz-button nzType="primary"><i nz-icon
        nzType="plus" style="margin:auto;margin-top: 0px;" nzTheme="outline"></i></button>
  </div>
</div>

<nz-form-control style="float: left;width: 75%;">&nbsp;</nz-form-control>
<nz-form-control style="float: left;width: 75%;">&nbsp;</nz-form-control>
<nz-form-control style="float: left;width: 75%;">&nbsp;</nz-form-control>
<nz-form-control style="float: left;width: 75%;">&nbsp;</nz-form-control>

<div class="footer">
  <button nz-button (click)="close()" class="btn">取消</button>
  <button nz-button nzType="primary" (click)="addEvent()" class="btn">确定</button>
</div>