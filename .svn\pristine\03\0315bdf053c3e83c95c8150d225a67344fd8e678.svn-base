<div class="news-detail-container">
    <nz-card [nzBordered]="false" [nzTitle]="titleTemplate" style="height: 100vh">
        <ng-template #titleTemplate>
            <div class="title-block">
                <span>涉我线索详情</span>
            </div>

            <div (click)="back()" style="position: absolute; right: 0; top: 20px; margin-right: 24px; display: flex; align-items: center; cursor: pointer">
                <i style="font-size: 18px; margin-right: 11px" nz-icon nzType="arrow-left" nzTheme="outline"></i>
                <span>返回</span>
            </div>
        </ng-template>

        <div style="display: flex; justify-content: space-between">
            <!-- 左 -->
            <div class="top-info white-block">
                <div class="news-info">
                    <div class="news-title">{{ topic_base_info.title }}</div>
                    <div>
                        <!-- <div nz-row> -->

                        <div nz-col>
                            <div class="base-item">
                                <span class="base-item-feild"><i nz-icon nzType="pushpin" nzTheme="twotone"></i>线索类别：</span>
                                <div class="base-item-value">
                                    <nz-tag *ngIf="topic_base_info.category_label" [nzColor]="topic_base_info.category_label | categoryColorFilter">{{ topic_base_info.category_label }}</nz-tag>
                                </div>
                            </div>
                        </div>

                        <div nz-col>
                            <div class="base-item">
                                <span class="base-item-feild"><i nz-icon nzType="tags" nzTheme="twotone"></i>标签：</span>

                                <div class="base-item-value">
                                    <nz-tag *ngFor="let item of topic_base_info.custom_labels" nz-tooltip [nzTitle]="'自定义标签：' + item">{{ item }}</nz-tag>
                                </div>
                            </div>
                        </div>
                        <div nz-col>
                            <div class="base-item">
                                <span class="base-item-feild"> <i nz-icon nzType="clock-circle" nzTheme="twotone"></i> 上报人：</span>
                                <span class="base-item-value"></span>
                            </div>
                        </div>
                        <div nz-col>
                            <div class="base-item">
                                <span class="base-item-feild"> <i nz-icon nzType="clock-circle" nzTheme="twotone"></i> 上报时间：</span>
                                <span class="base-item-value">{{ topic_base_info.pub_time | dateFilter }}</span>
                            </div>
                        </div>
                        <div nz-col>
                            <div class="base-item">
                                <span class="base-item-feild"> <i nz-icon nzType="clock-circle" nzTheme="twotone"></i> 威胁评分：</span>
                                <span class="base-item-value">80</span>
                            </div>
                        </div>

                        <div nz-col>
                            <div class="base-item">
                                <span class="base-item-feild"> <i style="color: #007aff" nz-icon nzType="pushpin" nzTheme="outline"></i> 字段一</span>
                                <span class="base-item-value">字段内容</span>
                            </div>
                        </div>
                        <div nz-col>
                            <div class="base-item">
                                <span class="base-item-feild"> <i style="color: #007aff" nz-icon nzType="pushpin" nzTheme="outline"></i> 佐证材料</span>
                                <span class="base-item-value">截图</span>
                            </div>
                        </div>
                        <div nz-col>
                            <div class="base-item">
                                <span class="base-item-feild"> <i style="color: #007aff" nz-icon nzType="pushpin" nzTheme="outline"></i> 佐证材料</span>
                                <span class="base-item-value">附件</span>
                            </div>
                        </div>

                        <!-- <div nz-col nzSpan="12">
                                <div class="base-item">
                                    <span class="base-item-feild"> <i nz-icon nzType="like" nzTheme="twotone"></i>推荐值：</span>
                                    <span class="base-item-value">{{ topic_base_info.recommended_value < 0.1 ? 0.1 : (topic_base_info.recommended_value | number : "1.2-2") }}</span>
                                </div>
                            </div> -->
                        <!-- </div> -->
                        <!-- <div nz-row>
                            <div nz-col nzSpan="12">
                                <div class="base-item">
                                    <span class="base-item-feild"><i nz-icon nzType="pushpin" nzTheme="twotone"></i>情报类别：</span>
                                    <div class="base-item-value">
                                        <nz-tag *ngIf="topic_base_info.category_label" [nzColor]="topic_base_info.category_label | categoryColorFilter">{{ topic_base_info.category_label }}</nz-tag> -->

                        <!-- {{ topic_base_info.category_label }} -->
                        <!-- </div>
                                </div>
                            </div>
                            <div nz-col nzSpan="12">
                                <div class="base-item">
                                    <span class="base-item-feild"> <i nz-icon nzType="key" nzTheme="outline" style="color: #4eaaff"></i>关键字：</span>
                                    <div class="base-item-value">
                                        <nz-tag *ngFor="let item of topic_base_info.keywords" nz-tooltip [nzTitle]="'关键词：' + item">{{ item }}</nz-tag>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div nz-row>
                            <div nz-col nzSpan="12">
                                <div class="base-item">
                                    <span class="base-item-feild"><i nz-icon nzType="tag" nzTheme="twotone"></i>所属专题：</span>
                                    <div class="base-item-value">
                                        <nz-tag *ngFor="let item of topic_base_info.topic" nz-tooltip [nzTitle]="'专题：' + item">{{ item }}</nz-tag>
                                    </div>
                                </div>
                            </div>
                            <div nz-col nzSpan="12">
                                <div class="base-item">
                                    <span class="base-item-feild"><i nz-icon nzType="tags" nzTheme="twotone"></i>标签：</span>

                                    <div class="base-item-value">
                                        <nz-tag *ngFor="let item of topic_base_info.custom_labels" nz-tooltip [nzTitle]="'自定义标签：' + item">{{ item }}</nz-tag>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <!-- url -->
                        <!-- <div nz-row>
                            <div nz-col nzSpan="24">
                                <div class="base-item">
                                    <span class="base-item-feild"> <i nz-icon nzType="api" nzTheme="twotone"></i>URL： </span>
                                    <div class="base-item-value">
                                        <a href="{{ topic_base_info.url }}" target="_blank" nz-tooltip [nzTitle]="topic_base_info.url">{{ topic_base_info.url }}</a>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <!-- <div class="brief-wrap">
                        <div class="brief">
                      
                            <news-description [description]="topic_base_info.content" maxLength="500"></news-description>
                        </div>
                    </div> -->
                    </div>
                </div>
            </div>

            <div class="report-temp">
                <!-- 报告模版 -->
                <div style="display: flex">
                    <div class="report-temp-title">选择模版</div>
                    <nz-select style="width: 120px" [(ngModel)]="selectedValue" nzAllowClear nzPlaceHolder="Choose">
                        <nz-option nzValue="jack" nzLabel="Jack"></nz-option>
                        <nz-option nzValue="选项1" nzLabel="选项1"></nz-option>
                        <nz-option nzValue="disabled" nzLabel="Disabled"></nz-option>
                    </nz-select>
                </div>
                <!-- 线索报告生成 -->
                <div class="report-genera">
                    <button (click)="toClueReport()" nz-button nzType="primary">线索报告生成</button>
                    <button nz-button nzType="default" style="margin-right: 20px">编辑</button>
                </div>
                <div class="content">工作发现，有攻击者使用恶意程序，‘恶意程序名’对我实施攻击。经分析，该恶意程序具有‘恶意程序功能’，并将窃取的数据加密回传至指定IP地址‘回传IP’或域名‘回传域名’</div>
            </div>
        </div>
    </nz-card>
</div>
