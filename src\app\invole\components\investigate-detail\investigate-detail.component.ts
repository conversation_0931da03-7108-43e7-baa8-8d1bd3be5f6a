import { Component, OnInit } from '@angular/core';
import { Observable } from "rxjs";
import { ActivatedRoute, Router, NavigationEnd } from "@angular/router";
import { EChartsOption } from "echarts/lib/echarts";
import {

    getWordCloudOption,

} from "app/invole/tools/echarts-option";
import { NzMessageService } from 'ng-zorro-antd';

import { IntelligenceTargetService } from '../../services/intelligence-target.service';
@Component({
    selector: 'app-investigate-detail',
    templateUrl: './investigate-detail.component.html',
    styleUrls: ['./investigate-detail.component.styl']
})
export class InvestigateDetailComponent implements OnInit {
    queryParams: Observable<any>;
    name: string = null
    isGroup: boolean = false;
    isVisible = false;
    isVisibleUser = false;
    // 词云图
    wordCloudChartOption: EChartsOption;
    listOfData = [];
    userListOfData = []
    // 信息列表
    listOfDataInfo = []
    listOfDataAll = [];
    // 返回的tabs数据
    tabIndex = 0;

    // 相似群组
    similarGroups = [];
    // 疑似地理位置
    suspectedLocation = [];

    members_info = {}
    // 群组信息
    group_basic_info_list = [];


    params = {
        group_id: null,
        page_size: 10,
        page_num: 1
    }
    // 群组成员总数
    group_member_Total = 0;
    group_info = {
        group_id: null,
        pageSize: 10,
        pageNum: 1
    }
    group_id = null;


    // 成员
    member_info = {
        member_id: null,
        page_size: 10,
        page_num: 1
    }
    member_info_Total = 0;
    member_id = null;

    // 获取成员所属群组列表
    member_group_Info = {
        member_id: null,
        pageSize: 10,
        pageNum: 1
    }
    // 群组标签
    group_basic_info = []
    // 成员标签
    Member_basic_info = []


    // 词频展示
    showWordCloud = false;


    private navigateToDetail(tabIndex: number) {
        this.router.navigate(["/invole/intelligence-investigate"], { queryParams: { tabsIndex: tabIndex } });

    }

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private msg: NzMessageService,
        private service: IntelligenceTargetService,

    ) { }

    ngOnInit() {

        this.member_info_Total = 0;
        this.route.queryParams.subscribe(async (queryParams) => {
            // console.log(queryParams, 'queryParams');
            this.tabIndex = parseInt(queryParams["tabsIndex"]);
            if (queryParams.isGroup === 'true') {
                this.isGroup = true
                this.name = queryParams["group_name"];
                this.params.group_id = queryParams["group_id"];
                this.group_info.group_id = queryParams["group_id"];
                this.group_id = queryParams["group_id"];
                this.getGroupList();
                this.getGroupInfo();
                this.getGroupWordCloud()
                this.getGroupSimilar()
            } else {
                this.member_info.member_id = queryParams["member_id"];
                this.member_id = queryParams["member_id"];
                this.member_group_Info.member_id = queryParams["member_id"];
                this.isGroup = false
                this.name = queryParams["member_name"];
                // 成员
                this.getMemberInfo()
                this.getMemberLocation()
                this.getMemberGroupInfo()
            }
        });
    }
    // 群组成员分页
    groupMemberPageSizeChanged(pageNumber: number): void {
        this.group_info.pageNum = pageNumber;
        this.getGroupInfo();
    }

    groupMemberPageChanged(pageSize: number): void {
        this.group_info.pageSize = pageSize;
        this.group_info.pageNum = 1;
        this.getGroupInfo();
    }

    onPageChange(pageNumber: number): void {
        if (this.isGroup) {
            this.params.page_num = pageNumber;
            this.getGroupList();
        } else {
            this.member_info.page_num = pageNumber;
            this.getMemberInfo()
        }
    }
    onPageSizeChange(pageSize: number): void {
        if (this.isGroup) {
            this.params.page_size = pageSize;
            this.params.page_num = 1;
            this.getGroupList();
        } else {
            this.member_info.page_size = pageSize;
            this.member_info.page_num = 1;
            this.getMemberInfo()
        }
    }

    // 群组信息列表
    getGroupList() {
        this.service.getGroupInformationListNew(
            this.params
        ).subscribe(res => {
            // console.log(res, '===================群组信息列表');
            if (res && res.code === 200) {
                this.listOfDataAll = res.data.results;

                this.member_info_Total = res.data.count;
            } else {
                this.msg.error('获取群组信息列表失败');
            }
        }, (error) => {
            this.msg.error('获取群组信息列表失败');
        }
        )
    }

    // 获取群组信息以及所属群组成员列表
    getGroupInfo() {
        this.service.getGroupInformationListDetail(this.group_info).subscribe(res => {
            // console.log(res.data, '群组信息以及所属群组成员列表');
            if (res && res.code === 200) {
                this.group_basic_info_list = {
                    ...res.data.group_info,
                    isEdit: false
                }
                if (res.data.group_info.group_label) this.group_basic_info = res.data.group_info.group_label.split(',')

                this.listOfData = res.data.members_info.data;

                this.group_member_Total = res.data.members_info.total;
            } else {
                this.msg.error('获取群组信息以及所属群组成员列表失败');
            }
        }, (error) => {
            this.msg.error('获取群组信息以及所属群组成员列表失败');
        }
        )
    }
    // 编辑标签
    editGroupLabel(label: any) {

        label.isEdit = true;

    }
    // 群组保存
    saveEdit(item): void {
        this.service.getGroupInformationTagEdit({
            id: item.id,
            group_label: item.group_label,

        }).subscribe(res => {
            if (res && res.code === 200) {
                this.getGroupInfo();
                this.msg.success(res.msg);
                item.isEdit = false;

            }
        })
    }


    cancelEdit(item): void {
        item.isEdit = false;

    }

    // 用户编辑标签
    editLabel(data): void {
        data.isEditMember = true;

    }
    // 用户编辑标签保存
    saveEditUser(item): void {
        this.service.getMemberInformationTagEdit({
            id: item.id,
            member_label: item.member_label,
        }).subscribe(res => {
            if (res && res.code === 200) {
                item.isEditMember = false;
                this.msg.success(res.msg);
                this.getMemberGroupInfo()
            }
        })
    }
    // 用户编辑标签取消
    cancelEditUser(item): void {
        item.isEditMember = false;
    }


    // 获取群组词频分析
    getGroupWordCloud() {
        this.service.getGroupInformationListAnalysis({
            group_id: this.group_id
        }).subscribe(res => {
            // console.log(res, '群组词频分析');
            if (res && res.code === 200 && res.data !== null) {
                this.showWordCloud = true;
                let data = [];
                Object.entries(res.data).forEach(([cityName, number]) => {
                    data.push({
                        name: cityName,
                        value: number
                    })
                });
                this.wordCloudChartOption = getWordCloudOption(data);
                // console.log(this.wordCloudChartOption, '词云图');

            } else {
                this.showWordCloud = false;
            }
        }, (error) => {
            this.msg.error('获取群组词频分析失败');

        }
        )
    }


    // 获取相似群组
    getGroupSimilar() {
        this.service.getGroupInformationListSimilar({
            group_id: this.group_id
        }).subscribe(res => {
            // console.log(res, '相似群组');
            if (res && res.data.length > 0) {
                this.similarGroups = res.data;
            }
        }, (error) => {
            this.msg.error('获取相似群组失败');
        }
        )
    }

    // 成员详细信息
    getMemberInfo() {
        this.service.getMemberInformationDetail(this.member_info).subscribe(res => {
            // console.log(res, '成员详细信息');

            if (res && res.code === 200) {
                this.listOfDataAll = res.data.results;
                this.member_info_Total = res.data.count;
            } else {
                this.msg.error('获取成员详细信息失败');
            }
        }, (error) => {
            this.msg.error('获取成员详细信息失败');
        }
        )
    }

    // 成员疑似地理位置
    getMemberLocation() {
        this.service.getMemberInformationSuspectedLocation({
            member_id: this.member_id
        }).subscribe(res => {
            // console.log(res, '成员疑似地理位置');
            if (res && res.data.length > 0) {
                this.suspectedLocation = res.data;
            }
        },
            (error) => {
                this.msg.error('获取成员疑似地理位置失败');
            }
        )
    }



    // 成员获取成员所属群组列表
    getMemberGroupInfo() {
        this.service.getMemberInformationGetGroupList(this.member_group_Info).subscribe(res => {
            // console.log(res, '成员获取成员所属群组列表');
            if (res && res.code === 200) {

                this.members_info = {
                    ...res.data.members_info,
                    isEditMember: false
                };

                if (res.data.members_info.member_label) this.Member_basic_info = res.data.members_info.member_label.split(',')

                this.userListOfData = res.data.group_info.data;
                this.group_member_Total = res.data.group_info.total;
            } else {
                this.msg.error('获取成员获取成员所属群组列表失败');
            }
        },
            (error) => {
                this.msg.error('获取成员获取成员所属群组列表失败');
            }
        )
    }



    private navigate(entity: any, isGroup: boolean): void {
        this.router.navigate(["invole/investigate-detail"], {
            queryParams: {
                ...entity,
                isGroup: isGroup,
                // tabsIndex: this.selectedIndex,
            },

        });
    }


    // 返回
    goBack() {
        this.navigateToDetail(this.tabIndex);
    }
    // 查看人员详情
    showPersonnelDetails(data) {

        this.navigate(data, false);
        document.getElementById("layout").scrollTop = 0;


    }
    // 查看群组详情
    showGroupDetails(data) {
        this.navigate(data, true);
        document.getElementById("layout").scrollTop = 0;
    }
}





