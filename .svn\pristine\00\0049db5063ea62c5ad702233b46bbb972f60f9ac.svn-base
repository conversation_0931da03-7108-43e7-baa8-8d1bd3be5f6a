.dash-card
    background-color: #fff;
    // box-shadow: 8px 8px 5px rgba(168,167,167,.459);
    // border-radius: 7px;
    margin-bottom: 16px
    min-height: 360px
.flex-item
  display: flex
  justify-content: center
  align-items: center
.count-item
  padding:  10px 32px
  display: flex
  align-items: center
  justify-content: space-around
.count-img
  width: 40px
  height: 40px
  margin-right: 32px
.dash-chart
  height: 300px
  width: 100%
  position: relative
.dash-chart-top
  height: 250px
.fixed-table
  ::ng-deep 
    table
      table-layout: fixed
      td
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
.card-title
  font-size: 20px
  color: rgba(0,0,0,0.85)
  font-weight: bold
  position: absolute
  top: 24px
  left: 24px
.table-empty
  margin-top: 170px
.table-card
  min-height: 590px
.pt-50
    padding-top: 50px